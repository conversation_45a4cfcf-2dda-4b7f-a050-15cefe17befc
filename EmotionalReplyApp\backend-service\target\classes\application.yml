server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: emotional-reply-service
  
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    
    # HikariCP连接池配置
    hikari:
      minimum-idle: 5                    # 最小空闲连接数
      maximum-pool-size: 20              # 最大连接池大小
      auto-commit: true                  # 自动提交
      idle-timeout: 30000               # 空闲连接超时时间(毫秒)
      pool-name: EmotionalReplyHikariCP # 连接池名称
      max-lifetime: 1800000             # 连接最大生命周期(毫秒)
      connection-timeout: 30000         # 连接超时时间(毫秒)
      connection-test-query: SELECT 1   # 连接测试查询

  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true  # 下划线转驼峰
    cache-enabled: false                # 关闭二级缓存
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 打印SQL日志
  global-config:
    db-config:
      id-type: auto                     # 主键类型
      logic-delete-field: deleted       # 逻辑删除字段
      logic-delete-value: 1             # 逻辑删除值
      logic-not-delete-value: 0         # 逻辑未删除值
  mapper-locations: classpath*:mapper/**/*.xml

# 日志配置
logging:
  level:
    com.emotional.service: DEBUG
    com.baomidou.mybatisplus: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/emotional-reply-service.log
    max-size: 100MB
    max-history: 30

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# Swagger配置
springfox:
  documentation:
    swagger-ui:
      enabled: true
    swagger:
      v2:
        path: /api-docs

# 应用自定义配置
app:
  # 情感分析配置
  emotion:
    api-url: ${EMOTION_API_URL:http://localhost:8081/emotion/analyze}
    api-key: ${EMOTION_API_KEY:your-api-key}
    timeout: 5000
    
  # 回复生成配置
  reply:
    max-length: 200                     # 最大回复长度
    min-length: 10                      # 最小回复长度
    default-style: warm_caring          # 默认回复风格
    cache-expire: 3600                  # 缓存过期时间(秒)
    
  # 用户配置
  user:
    max-daily-requests: 100             # 每日最大请求数
    vip-max-daily-requests: 1000        # VIP用户每日最大请求数
    
  # 安全配置
  security:
    jwt-secret: ${JWT_SECRET:emotional-reply-secret-key}
    jwt-expire: 86400                   # JWT过期时间(秒)
    
  # 文件存储配置
  file:
    upload-path: ${FILE_UPLOAD_PATH:/tmp/uploads}
    max-size: 10485760                  # 最大文件大小(字节)

---
# 开发环境配置
spring:
  profiles: dev
  datasource:
    url: ****************************************************************************************************************************************************************

logging:
  level:
    root: INFO
    com.emotional.service: DEBUG

---
# 测试环境配置
spring:
  profiles: test
  datasource:
    url: ***************************************************************************************************************************************************************

---
# 生产环境配置
spring:
  profiles: prod
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:emotional_reply}?useUnicode=true&characterEncoding=utf8mb4&useSSL=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10

logging:
  level:
    root: WARN
    com.emotional.service: INFO
  file:
    name: /var/log/emotional-reply-service.log
