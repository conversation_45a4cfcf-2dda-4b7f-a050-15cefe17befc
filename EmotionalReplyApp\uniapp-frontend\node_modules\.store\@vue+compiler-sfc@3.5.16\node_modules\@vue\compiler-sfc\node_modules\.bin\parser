#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/Program Files/VsCodeProject/EmotionalReplyApp/uniapp-frontend/node_modules/.store/@babel+parser@7.27.5/node_modules/@babel"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/Program Files/VsCodeProject/EmotionalReplyApp/uniapp-frontend/node_modules/.store/@babel+parser@7.27.5/node_modules/@babel"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@babel+parser@7.27.5/node_modules/@babel/parser/bin/babel-parser.js" "$@"
else
  exec node  "$basedir/../../../../../../@babel+parser@7.27.5/node_modules/@babel/parser/bin/babel-parser.js" "$@"
fi
