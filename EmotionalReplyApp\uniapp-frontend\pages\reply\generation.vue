<template>
  <view class="container">
    <!-- 原始消息显示 -->
    <view class="original-message">
      <text class="label">收到的消息：</text>
      <text class="message-text">{{ originalMessage }}</text>
    </view>
    
    <!-- 情感分析结果 -->
    <view class="emotion-analysis" v-if="emotionResult">
      <text class="section-title">情感分析</text>
      <view class="emotion-tags">
        <text class="emotion-tag">{{ emotionResult.emotion }}</text>
        <text class="confidence">置信度: {{ emotionResult.confidence }}%</text>
      </view>
    </view>
    
    <!-- 回复建议 -->
    <view class="reply-suggestions">
      <text class="section-title">回复建议</text>
      
      <view v-if="loading" class="loading">
        <text>正在生成回复建议...</text>
      </view>
      
      <view v-else class="reply-list">
        <view 
          v-for="(reply, index) in replyList" 
          :key="index"
          class="reply-item"
        >
          <view class="reply-header">
            <text class="style-tag">{{ getStyleName(reply.style) }}</text>
            <view class="actions">
              <text class="copy-btn" @click="copyReply(reply.content)">复制</text>
              <text class="like-btn" @click="likeReply(reply)">👍</text>
            </view>
          </view>
          <text class="reply-content">{{ reply.content }}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部操作 -->
    <view class="bottom-actions">
      <button class="regenerate-btn" @click="regenerateReplies">
        <text>重新生成</text>
      </button>
      <button class="new-message-btn" @click="inputNewMessage">
        <text>输入新消息</text>
      </button>
    </view>
  </view>
</template>

<script>
import { HistoryManager } from '../../utils/storage.js'
import MockApiUtil from '../../utils/mockApi.js'
import { analyzeEmotion } from '../../api/emotion.js'

export default {
  name: 'ReplyGeneration',

  data() {
    return {
      originalMessage: '',
      loading: false,
      emotionResult: null,
      replyList: []
    }
  },
  
  onLoad(options) {
    if (options.message) {
      this.originalMessage = decodeURIComponent(options.message)
      this.generateReplies()
    }
  },
  
  methods: {
    // 生成回复
    async generateReplies() {
      this.loading = true

      try {
        // 尝试使用真实API进行情感分析和回复生成
        const response = await analyzeEmotion(
          this.originalMessage,
          ['warm_caring', 'humorous', 'rational', 'concise', 'romantic'],
          true
        )

        this.emotionResult = response.emotionResult
        this.replyList = response.replyOptions || []

        console.log('API响应:', response)

      } catch (error) {
        console.error('API调用失败，使用模拟数据:', error)

        // API调用失败时使用模拟数据
        try {
          this.emotionResult = await MockApiUtil.analyzeEmotion(this.originalMessage)
          this.replyList = await MockApiUtil.generateReplies(
            this.originalMessage,
            this.emotionResult.emotion
          )

          // 保存到本地历史记录
          HistoryManager.addHistory(
            this.originalMessage,
            this.replyList,
            this.emotionResult
          )
        } catch (mockError) {
          console.error('模拟API也失败了:', mockError)
          uni.showToast({
            title: '生成失败，请重试',
            icon: 'none'
          })
        }
      } finally {
        this.loading = false
      }
    },
    
    // 重新生成回复
    regenerateReplies() {
      this.generateReplies()
    },
    
    // 复制回复
    copyReply(content) {
      uni.setClipboardData({
        data: content,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },
    
    // 点赞回复
    likeReply(reply) {
      // 这里可以添加点赞统计逻辑
      console.log('用户点赞了回复:', reply)
      uni.showToast({
        title: '感谢反馈！',
        icon: 'success'
      })
    },
    
    // 输入新消息
    inputNewMessage() {
      uni.navigateBack()
    },
    
    // 获取风格名称
    getStyleName(style) {
      const styleMap = {
        warm_caring: '温暖关怀',
        humorous: '幽默风趣',
        rational: '理性分析',
        concise: '简洁直接',
        romantic: '浪漫情话'
      }
      return styleMap[style] || style
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.original-message {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .label {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 16rpx;
  }
  
  .message-text {
    font-size: 32rpx;
    color: #333;
    line-height: 1.5;
  }
}

.emotion-analysis {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .emotion-tags {
    display: flex;
    align-items: center;
    
    .emotion-tag {
      background: #2196F3;
      color: white;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      margin-right: 20rpx;
    }
    
    .confidence {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.reply-suggestions {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 100rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .loading {
    text-align: center;
    padding: 60rpx 0;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
  }
  
  .reply-list {
    .reply-item {
      border: 2rpx solid #f0f0f0;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
        
        .style-tag {
          background: rgba(33, 150, 243, 0.1);
          color: #2196F3;
          padding: 6rpx 12rpx;
          border-radius: 12rpx;
          font-size: 22rpx;
        }
        
        .actions {
          display: flex;
          align-items: center;
          
          .copy-btn, .like-btn {
            margin-left: 20rpx;
            font-size: 24rpx;
            color: #2196F3;
          }
        }
      }
      
      .reply-content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .regenerate-btn, .new-message-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      font-size: 28rpx;
      font-weight: bold;
    }
  }
  
  .regenerate-btn {
    background: #f0f0f0;
    
    text {
      color: #333;
    }
  }
  
  .new-message-btn {
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    
    text {
      color: white;
    }
  }
}
</style>
