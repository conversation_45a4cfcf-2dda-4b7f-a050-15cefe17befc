<template>
  <view class="container">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 应用标题区域 -->
      <view class="header">
        <view class="app-icon">
          <text class="icon-emoji">💬</text>
        </view>
        <text class="app-name">情感回复助手</text>
        <text class="app-desc">智能分析，贴心回复</text>
      </view>
    
      <!-- 登录表单卡片 -->
      <view class="form-section">
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">👤</text>
            <input
              v-model="loginForm.username"
              class="input-field"
              placeholder="请输入用户名/手机号"
              :maxlength="20"
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input
              v-model="loginForm.password"
              class="input-field"
              placeholder="请输入密码"
              :type="showPassword ? 'text' : 'password'"
              :maxlength="20"
            />
            <text class="password-toggle" @click="togglePassword">
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </text>
          </view>
        </view>

        <view class="form-options">
          <view class="remember-section" @click="toggleRemember">
            <view class="checkbox" :class="{ checked: rememberPassword }">
              <text class="check-icon" v-if="rememberPassword">✓</text>
            </view>
            <text class="remember-text">记住密码</text>
          </view>
          <text class="forgot-password" @click="forgotPassword">忘记密码？</text>
        </view>
      
        <button
          class="login-btn"
          :class="{ disabled: !canLogin, loading: loading }"
          @click="handleLogin"
        >
          <text class="btn-text">{{ loading ? '登录中...' : '登录' }}</text>
          <view class="loading-spinner" v-if="loading"></view>
        </button>

        <view class="register-section">
          <text class="register-text">还没有账号？</text>
          <text class="register-link" @click="goRegister">立即注册</text>
        </view>
      </view>

      <!-- 快速登录区域 -->
      <view class="quick-login">
        <view class="divider">
          <view class="divider-line"></view>
          <text class="divider-text">快速登录</text>
          <view class="divider-line"></view>
        </view>

        <view class="quick-buttons">
          <button class="quick-btn wechat" @click="wechatLogin">
            <text class="btn-icon">💬</text>
            <text class="btn-text">微信登录</text>
          </button>
          <button class="quick-btn guest" @click="guestLogin">
            <text class="btn-icon">🎭</text>
            <text class="btn-text">游客体验</text>
          </button>
        </view>
      </view>
    </view>
    
    <view class="footer">
      <text class="agreement">
        登录即表示同意
        <text class="link-text" @click="showAgreement">《用户协议》</text>
        和
        <text class="link-text" @click="showPrivacy">《隐私政策》</text>
      </text>
    </view>
  </view>
</template>

<script>
import { login } from '../../api/user.js'
import { UserManager } from '../../utils/user.js'

export default {
  name: 'LoginPage',
  
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      rememberPassword: false,
      loading: false,
      showPassword: false
    }
  },
  
  computed: {
    canLogin() {
      return this.loginForm.username.trim() && 
             this.loginForm.password.trim() && 
             !this.loading
    }
  },
  
  onLoad() {
    this.loadSavedCredentials()
  },
  
  methods: {
    // 加载保存的凭据
    loadSavedCredentials() {
      const saved = uni.getStorageSync('savedCredentials')
      if (saved) {
        this.loginForm = saved
        this.rememberPassword = true
      }
    },
    


    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword
    },

    // 切换记住密码
    toggleRemember() {
      this.rememberPassword = !this.rememberPassword
    },

    // 记住密码选择（兼容旧版本）
    onRememberChange(e) {
      this.rememberPassword = e.detail.value.length > 0
    },
    
    // 处理登录
    async handleLogin() {
      if (!this.canLogin) return

      this.loading = true

      try {
        // 调用真实登录API
        const response = await login(this.loginForm.username, this.loginForm.password)

        // 保存用户信息和Token
        UserManager.setUserInfo(response.userInfo)
        UserManager.setToken(response.token)

        // 保存密码（如果选择记住）
        if (this.rememberPassword) {
          uni.setStorageSync('savedCredentials', this.loginForm)
        } else {
          uni.removeStorageSync('savedCredentials')
        }

        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到首页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }, 1500)

      } catch (error) {
        console.error('登录失败:', error)

        // 如果是网络错误，允许使用演示模式
        if (error.message && error.message.includes('网络')) {
          uni.showModal({
            title: '网络连接失败',
            content: '无法连接到服务器，是否使用演示模式？',
            success: (res) => {
              if (res.confirm) {
                this.enterDemoMode()
              }
            }
          })
        } else {
          uni.showToast({
            title: error.message || '登录失败，请检查用户名和密码',
            icon: 'none'
          })
        }
      } finally {
        this.loading = false
      }
    },

    // 进入演示模式
    enterDemoMode() {
      const demoUser = {
        id: 1,
        username: this.loginForm.username || 'demo',
        nickname: 'yumu',
        email: '<EMAIL>',
        avatar: '/static/images/default-avatar.png',
        isVip: false,
        dailyQuota: 10,
        todayUsed: 0,
        totalUsed: 0
      }

      UserManager.setUserInfo(demoUser)
      UserManager.setToken('demo_token_' + Date.now())

      uni.showToast({
        title: '已进入演示模式',
        icon: 'success'
      })

      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)
    },
    
    // 忘记密码
    forgotPassword() {
      uni.navigateTo({
        url: '/pages/user/forgot-password'
      })
    },
    
    // 去注册
    goRegister() {
      uni.navigateTo({
        url: '/pages/user/register'
      })
    },
    
    // 微信登录
    wechatLogin() {
      uni.showToast({
        title: '微信登录功能开发中',
        icon: 'none'
      })
    },
    
    // 游客登录
    guestLogin() {
      const guestInfo = {
        id: 0,
        username: 'guest',
        nickname: '游客',
        avatar: '/static/images/guest-avatar.png',
        isVip: false
      }
      
      uni.setStorageSync('userInfo', guestInfo)
      uni.setStorageSync('isGuest', true)
      
      uni.showToast({
        title: '游客登录成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)
    },
    
    // 显示用户协议
    showAgreement() {
      uni.navigateTo({
        url: '/pages/legal/agreement'
      })
    },
    
    // 显示隐私政策
    showPrivacy() {
      uni.navigateTo({
        url: '/pages/legal/privacy'
      })
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 主要内容区域
.main-content {
  flex: 1;
  padding: 80rpx 30rpx 40rpx;
  display: flex;
  flex-direction: column;
}

// 应用标题区域
.header {
  text-align: center;
  margin-bottom: 80rpx;

  .app-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 30rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);

    .icon-emoji {
      font-size: 60rpx;
    }
  }

  .app-name {
    display: block;
    color: white;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
  }

  .app-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
  }
}

// 登录表单卡片
.form-section {
  background: white;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

  .input-group {
    margin-bottom: 30rpx;

    .input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      transition: all 0.3s ease;

      &:focus-within {
        border-color: #2196F3;
        background: white;
        box-shadow: 0 0 0 6rpx rgba(33, 150, 243, 0.1);
      }

      .input-icon {
        padding: 0 20rpx;
        font-size: 32rpx;
        color: #6c757d;
      }

      .input-field {
        flex: 1;
        height: 88rpx;
        padding: 0 20rpx;
        border: none;
        background: transparent;
        font-size: 28rpx;
        color: #333;

        &::placeholder {
          color: #adb5bd;
        }
      }

      .password-toggle {
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #6c757d;
        cursor: pointer;
      }
    }
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .remember-section {
      display: flex;
      align-items: center;
      cursor: pointer;

      .checkbox {
        width: 36rpx;
        height: 36rpx;
        border: 2rpx solid #dee2e6;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;
        transition: all 0.3s ease;

        &.checked {
          background: #2196F3;
          border-color: #2196F3;

          .check-icon {
            color: white;
            font-size: 20rpx;
            font-weight: bold;
          }
        }
      }

      .remember-text {
        font-size: 26rpx;
        color: #666;
      }
    }

    .forgot-password {
      font-size: 26rpx;
      color: #2196F3;
      cursor: pointer;
    }
  }
  
  .login-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    border-radius: 16rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    .btn-text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }

    .loading-spinner {
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid rgba(255, 255, 255, 0.3);
      border-top: 3rpx solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 16rpx;
    }

    &.disabled {
      background: #dee2e6;

      .btn-text {
        color: #6c757d;
      }
    }

    &.loading {
      pointer-events: none;
    }

    &:active {
      transform: translateY(2rpx);
    }
  }

  .register-section {
    text-align: center;

    .register-text {
      font-size: 26rpx;
      color: #666;
    }

    .register-link {
      color: #2196F3;
      margin-left: 8rpx;
      font-size: 26rpx;
      cursor: pointer;
    }
  }
}

// 快速登录区域
.quick-login {
  margin-bottom: 40rpx;

  .divider {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .divider-line {
      flex: 1;
      height: 1rpx;
      background: rgba(255, 255, 255, 0.3);
    }

    .divider-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 24rpx;
      padding: 0 20rpx;
    }
  }

  .quick-buttons {
    display: flex;
    gap: 20rpx;

    .quick-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 16rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10rpx);
      transition: all 0.3s ease;

      .btn-icon {
        font-size: 28rpx;
        margin-right: 8rpx;
      }

      .btn-text {
        color: white;
        font-size: 26rpx;
      }

      &.wechat {
        background: rgba(7, 193, 96, 0.2);
        border-color: rgba(7, 193, 96, 0.5);
      }

      &.guest {
        background: rgba(255, 255, 255, 0.15);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

// 底部协议
.footer {
  margin-top: auto;
  padding: 20rpx 0;

  .agreement {
    text-align: center;
    font-size: 22rpx;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;

    .link-text {
      color: white;
      text-decoration: underline;
      cursor: pointer;
    }
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
