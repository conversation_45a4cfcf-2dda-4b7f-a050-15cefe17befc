<template>
  <view class="container">
    <view class="header">
      <image src="/static/logo.png" class="logo" />
      <text class="app-name">情感回复助手</text>
      <text class="app-desc">智能分析，贴心回复</text>
    </view>
    
    <view class="form-section">
      <view class="input-group">
        <input 
          v-model="loginForm.username"
          class="input-field"
          placeholder="请输入用户名/手机号"
          :maxlength="20"
        />
      </view>
      
      <view class="input-group">
        <input 
          v-model="loginForm.password"
          class="input-field"
          placeholder="请输入密码"
          password
          :maxlength="20"
        />
      </view>
      
      <view class="form-options">
        <label class="checkbox-item">
          <checkbox :checked="rememberPassword" @change="onRememberChange" />
          <text>记住密码</text>
        </label>
        <text class="forgot-password" @click="forgotPassword">忘记密码？</text>
      </view>
      
      <button 
        class="login-btn"
        :disabled="!canLogin"
        @click="handleLogin"
      >
        <text>{{ loading ? '登录中...' : '登录' }}</text>
      </button>
      
      <view class="register-link">
        <text>还没有账号？</text>
        <text class="link-text" @click="goRegister">立即注册</text>
      </view>
    </view>
    
    <view class="quick-login">
      <text class="quick-title">快速登录</text>
      <view class="quick-buttons">
        <button class="quick-btn wechat" @click="wechatLogin">
          <text>微信登录</text>
        </button>
        <button class="quick-btn guest" @click="guestLogin">
          <text>游客体验</text>
        </button>
      </view>
    </view>
    
    <view class="footer">
      <text class="agreement">
        登录即表示同意
        <text class="link-text" @click="showAgreement">《用户协议》</text>
        和
        <text class="link-text" @click="showPrivacy">《隐私政策》</text>
      </text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LoginPage',
  
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      rememberPassword: false,
      loading: false
    }
  },
  
  computed: {
    canLogin() {
      return this.loginForm.username.trim() && 
             this.loginForm.password.trim() && 
             !this.loading
    }
  },
  
  onLoad() {
    this.loadSavedCredentials()
  },
  
  methods: {
    // 加载保存的凭据
    loadSavedCredentials() {
      const saved = uni.getStorageSync('savedCredentials')
      if (saved) {
        this.loginForm = saved
        this.rememberPassword = true
      }
    },
    
    // 记住密码选择
    onRememberChange(e) {
      this.rememberPassword = e.detail.value.length > 0
    },
    
    // 处理登录
    async handleLogin() {
      if (!this.canLogin) return
      
      this.loading = true
      
      try {
        // 模拟登录API调用
        await this.delay(2000)
        
        // 模拟登录成功
        const userInfo = {
          id: 1,
          username: this.loginForm.username,
          nickname: '用户' + this.loginForm.username,
          avatar: '/static/images/default-avatar.png',
          isVip: false
        }
        
        const token = 'mock_token_' + Date.now()
        
        // 保存用户信息
        uni.setStorageSync('token', token)
        uni.setStorageSync('userInfo', userInfo)
        
        // 保存密码（如果选择记住）
        if (this.rememberPassword) {
          uni.setStorageSync('savedCredentials', this.loginForm)
        } else {
          uni.removeStorageSync('savedCredentials')
        }
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 跳转到首页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }, 1500)
        
      } catch (error) {
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 忘记密码
    forgotPassword() {
      uni.navigateTo({
        url: '/pages/user/forgot-password'
      })
    },
    
    // 去注册
    goRegister() {
      uni.navigateTo({
        url: '/pages/user/register'
      })
    },
    
    // 微信登录
    wechatLogin() {
      uni.showToast({
        title: '微信登录功能开发中',
        icon: 'none'
      })
    },
    
    // 游客登录
    guestLogin() {
      const guestInfo = {
        id: 0,
        username: 'guest',
        nickname: '游客用户',
        avatar: '/static/images/guest-avatar.png',
        isVip: false
      }
      
      uni.setStorageSync('userInfo', guestInfo)
      uni.setStorageSync('isGuest', true)
      
      uni.showToast({
        title: '游客登录成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)
    },
    
    // 显示用户协议
    showAgreement() {
      uni.navigateTo({
        url: '/pages/legal/agreement'
      })
    },
    
    // 显示隐私政策
    showPrivacy() {
      uni.navigateTo({
        url: '/pages/legal/privacy'
      })
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 60rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    border-radius: 24rpx;
    margin-bottom: 30rpx;
  }
  
  .app-name {
    display: block;
    color: white;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
  }
  
  .app-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
  }
}

.form-section {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  
  .input-group {
    margin-bottom: 40rpx;
    
    .input-field {
      width: 100%;
      height: 88rpx;
      padding: 0 30rpx;
      border: 2rpx solid #e0e0e0;
      border-radius: 44rpx;
      font-size: 28rpx;
      background-color: #fafafa;
      
      &:focus {
        border-color: #2196F3;
      }
    }
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50rpx;
    
    .checkbox-item {
      display: flex;
      align-items: center;
      
      text {
        margin-left: 16rpx;
        font-size: 26rpx;
        color: #666;
      }
    }
    
    .forgot-password {
      font-size: 26rpx;
      color: #2196F3;
    }
  }
  
  .login-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
    
    text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
    
    &:disabled {
      background: #ccc;
    }
  }
  
  .register-link {
    text-align: center;
    
    text {
      font-size: 26rpx;
      color: #666;
    }
    
    .link-text {
      color: #2196F3;
      margin-left: 8rpx;
    }
  }
}

.quick-login {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  
  .quick-title {
    display: block;
    text-align: center;
    color: white;
    font-size: 28rpx;
    margin-bottom: 30rpx;
  }
  
  .quick-buttons {
    display: flex;
    gap: 20rpx;
    
    .quick-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
      background: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      
      text {
        color: white;
        font-size: 26rpx;
      }
      
      &.wechat {
        background: rgba(7, 193, 96, 0.2);
      }
      
      &.guest {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

.footer {
  margin-top: auto;
  
  .agreement {
    text-align: center;
    font-size: 22rpx;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
    
    .link-text {
      color: white;
      text-decoration: underline;
    }
  }
}
</style>
