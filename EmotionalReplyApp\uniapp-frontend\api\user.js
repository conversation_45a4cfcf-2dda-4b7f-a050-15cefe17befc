/**
 * 用户相关 API
 */
import { post, get, put } from '../utils/request.js'

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise} 登录结果
 */
export const login = (username, password) => {
  return post('/user/login', {
    username,
    password
  })
}

/**
 * 获取用户信息
 * @param {number} userId - 用户ID
 * @returns {Promise} 用户信息
 */
export const getUserInfo = (userId) => {
  return get(`/user/info/${userId}`)
}

/**
 * 更新用户信息
 * @param {number} userId - 用户ID
 * @param {object} userInfo - 用户信息
 * @returns {Promise} 更新结果
 */
export const updateUserInfo = (userId, userInfo) => {
  return put(`/user/info/${userId}`, userInfo)
}

/**
 * 修改密码
 * @param {number} userId - 用户ID
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 * @returns {Promise} 修改结果
 */
export const changePassword = (userId, oldPassword, newPassword) => {
  return put(`/user/password/${userId}`, {
    oldPassword,
    newPassword
  })
}

/**
 * 上传头像
 * @param {number} userId - 用户ID
 * @param {string} filePath - 文件路径
 * @returns {Promise} 上传结果
 */
export const uploadAvatar = (userId, filePath) => {
  return upload(`/user/avatar/${userId}`, filePath)
}

export default {
  login,
  getUserInfo,
  updateUserInfo,
  changePassword,
  uploadAvatar
}
