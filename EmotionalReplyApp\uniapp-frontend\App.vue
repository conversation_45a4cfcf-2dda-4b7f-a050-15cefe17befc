<template>
  <view id="app">
    <!-- 全局加载提示 -->
    <view
      v-if="globalLoading"
      class="global-loading"
    >
      <text>正在加载...</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'App',

  data() {
    return {
      globalLoading: false
    }
  },

  onLaunch() {
    console.log('App Launch')
    this.initApp()
  },

  onShow() {
    console.log('App Show')
  },

  onHide() {
    console.log('App Hide')
  },

  methods: {
    // 初始化应用
    async initApp() {
      try {
        this.globalLoading = true

        // 模拟初始化过程
        await this.delay(1000)

        console.log('App initialized successfully')

      } catch (error) {
        console.error('App initialization failed:', error)
        uni.showToast({
          title: '应用初始化失败',
          icon: 'none'
        })
      } finally {
        this.globalLoading = false
      }
    },

    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss">
@import './static/css/global.css';

#app {
  height: 100vh;
  position: relative;
}

.global-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}
</style>
