<template>
  <view id="app">
    <!-- 全局加载提示 -->
    <uni-load-more 
      v-if="globalLoading" 
      status="loading" 
      :content-text="loadingText"
      class="global-loading"
    />
    
    <!-- 悬浮气泡组件 -->
    <floating-bubble 
      v-if="showFloatingBubble && !globalLoading"
      @click="handleBubbleClick"
    />
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import FloatingBubble from './components/floating-bubble/floating-bubble.vue'

export default {
  name: 'App',
  components: {
    FloatingBubble
  },
  
  data() {
    return {
      globalLoading: false,
      loadingText: {
        contentdown: '上拉显示更多',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      }
    }
  },
  
  computed: {
    ...mapState('settings', ['showFloatingBubble']),
    ...mapState('user', ['isLoggedIn'])
  },
  
  onLaunch() {
    console.log('App Launch')
    this.initApp()
  },
  
  onShow() {
    console.log('App Show')
    this.checkPermissions()
  },
  
  onHide() {
    console.log('App Hide')
  },
  
  methods: {
    ...mapActions('user', ['checkLoginStatus']),
    ...mapActions('settings', ['loadSettings']),
    
    // 初始化应用
    async initApp() {
      try {
        this.globalLoading = true
        
        // 加载用户设置
        await this.loadSettings()
        
        // 检查登录状态
        await this.checkLoginStatus()
        
        // 初始化原生插件
        await this.initNativePlugins()
        
      } catch (error) {
        console.error('App initialization failed:', error)
        uni.showToast({
          title: '应用初始化失败',
          icon: 'none'
        })
      } finally {
        this.globalLoading = false
      }
    },
    
    // 初始化原生插件
    async initNativePlugins() {
      // #ifdef APP-PLUS
      try {
        // 初始化悬浮窗插件
        const floatingWindow = uni.requireNativePlugin('floating-window-plugin')
        if (floatingWindow) {
          await floatingWindow.init()
          console.log('Floating window plugin initialized')
        }
      } catch (error) {
        console.error('Native plugin initialization failed:', error)
      }
      // #endif
    },
    
    // 检查权限
    checkPermissions() {
      // #ifdef APP-PLUS
      // 检查悬浮窗权限
      this.checkOverlayPermission()
      // #endif
    },
    
    // 检查悬浮窗权限
    checkOverlayPermission() {
      // #ifdef APP-PLUS
      const permissionUtils = uni.requireNativePlugin('floating-window-plugin')
      if (permissionUtils) {
        permissionUtils.hasOverlayPermission((result) => {
          if (!result.hasPermission) {
            this.requestOverlayPermission()
          }
        })
      }
      // #endif
    },
    
    // 请求悬浮窗权限
    requestOverlayPermission() {
      uni.showModal({
        title: '权限申请',
        content: '为了更好的使用体验，需要开启悬浮窗权限',
        confirmText: '去设置',
        cancelText: '暂不开启',
        success: (res) => {
          if (res.confirm) {
            // #ifdef APP-PLUS
            const permissionUtils = uni.requireNativePlugin('floating-window-plugin')
            if (permissionUtils) {
              permissionUtils.requestOverlayPermission()
            }
            // #endif
          }
        }
      })
    },
    
    // 处理悬浮气泡点击
    handleBubbleClick() {
      // 跳转到消息输入页面
      uni.navigateTo({
        url: '/pages/message/input'
      })
    }
  }
}
</script>

<style lang="scss">
@import './static/css/global.scss';

#app {
  height: 100vh;
  position: relative;
}

.global-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}
</style>
