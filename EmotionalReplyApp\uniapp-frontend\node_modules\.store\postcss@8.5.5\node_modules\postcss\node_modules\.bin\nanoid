#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/Program Files/VsCodeProject/EmotionalReplyApp/uniapp-frontend/node_modules/.store/nanoid@3.3.11/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/Program Files/VsCodeProject/EmotionalReplyApp/uniapp-frontend/node_modules/.store/nanoid@3.3.11/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../nanoid@3.3.11/node_modules/nanoid/bin/nanoid.cjs" "$@"
else
  exec node  "$basedir/../../../../../nanoid@3.3.11/node_modules/nanoid/bin/nanoid.cjs" "$@"
fi
