{"name": "emotional-reply-app", "version": "1.0.0", "description": "情感回复助手 uni-app 应用", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-serve", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-serve", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickstart": "node node_modules/@dcloudio/uni-migration/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest", "test:h5": "cross-env UNI_PLATFORM=h5 jest", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-app-plus": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "@dcloudio/uni-ui": "^1.4.28", "vue": "^3.2.47", "vuex": "^4.0.2", "pinia": "^2.0.36", "axios": "^1.4.0", "dayjs": "^1.11.7", "lodash": "^4.17.21", "crypto-js": "^4.1.1"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^3.0.0", "@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vue-cli-plugin-uni": "^3.0.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.13.6", "cross-env": "^7.0.3", "jest": "^29.5.0", "mini-types": "*", "postcss-comment": "^2.0.0", "typescript": "^4.9.5"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}, "repository": {"type": "git", "url": "https://github.com/your-username/emotional-reply-app.git"}, "keywords": ["uni-app", "emotional", "reply", "assistant", "vue3"], "author": "Your Name", "license": "MIT"}