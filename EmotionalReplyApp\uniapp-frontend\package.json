{"name": "emotional-reply-app", "version": "1.0.0", "description": "情感回复助手 uni-app 应用", "main": "main.js", "scripts": {"dev:app": "uni build --platform app", "dev:h5": "uni serve", "dev:mp-weixin": "uni build --platform mp-weixin --watch", "build:app": "uni build --platform app --mode production", "build:h5": "uni build --platform h5 --mode production", "build:mp-weixin": "uni build --platform mp-weixin --mode production"}, "dependencies": {"axios": "^1.4.0", "@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "vue": "^3.3.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "vite": "^4.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}, "repository": {"type": "git", "url": "https://github.com/your-username/emotional-reply-app.git"}, "keywords": ["uni-app", "emotional", "reply", "assistant", "vue3"], "author": "YUMU", "license": "MIT"}