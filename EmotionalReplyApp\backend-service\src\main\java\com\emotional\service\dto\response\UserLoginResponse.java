package com.emotional.service.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户登录响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "用户登录响应")
public class UserLoginResponse {
    
    @Schema(description = "访问令牌")
    private String token;
    
    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";
    
    @Schema(description = "令牌过期时间（秒）", example = "7200")
    private Long expiresIn = 7200L;
    
    @Schema(description = "用户信息")
    private UserInfo userInfo;
    
    /**
     * 用户信息内部类
     */
    @Data
    @Schema(description = "用户信息")
    public static class UserInfo {
        
        @Schema(description = "用户ID")
        private Long id;
        
        @Schema(description = "用户名")
        private String username;
        
        @Schema(description = "昵称")
        private String nickname;
        
        @Schema(description = "邮箱")
        private String email;
        
        @Schema(description = "手机号")
        private String phone;
        
        @Schema(description = "头像URL")
        private String avatar;
        
        @Schema(description = "是否VIP")
        private Boolean isVip;
        
        @Schema(description = "VIP过期时间")
        private LocalDateTime vipExpireTime;
        
        @Schema(description = "每日配额")
        private Integer dailyQuota;
        
        @Schema(description = "今日已使用")
        private Integer todayUsed;
        
        @Schema(description = "总使用次数")
        private Integer totalUsed;
        
        @Schema(description = "用户状态")
        private Integer status;
        
        @Schema(description = "最后登录时间")
        private LocalDateTime lastLoginTime;
    }
}
