<template>
  <view class="container">
    <view class="header">
      <text class="title">API 连接测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">1. 测试回复风格API</text>
      <button class="test-btn" @click="testReplyStyles" :loading="loading1">
        获取回复风格
      </button>
      <view class="result" v-if="stylesResult">
        <text class="result-title">结果:</text>
        <text class="result-content">{{ stylesResult }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <text class="section-title">2. 测试情感分析API</text>
      <input class="test-input" v-model="testMessage" placeholder="输入测试消息" />
      <button class="test-btn" @click="testEmotionAnalysis" :loading="loading2">
        分析情感
      </button>
      <view class="result" v-if="emotionResult">
        <text class="result-title">结果:</text>
        <text class="result-content">{{ emotionResult }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <text class="section-title">3. 测试历史记录API</text>
      <button class="test-btn" @click="testHistory" :loading="loading3">
        获取历史记录
      </button>
      <view class="result" v-if="historyResult">
        <text class="result-title">结果:</text>
        <text class="result-content">{{ historyResult }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <text class="section-title">4. 测试用户统计API</text>
      <button class="test-btn" @click="testUserStats" :loading="loading4">
        获取用户统计
      </button>
      <view class="result" v-if="statsResult">
        <text class="result-title">结果:</text>
        <text class="result-content">{{ statsResult }}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">5. 用户信息测试</text>
      <view class="user-info">
        <text class="info-text">当前用户: {{ currentUser.nickname || currentUser.username }}</text>
        <text class="info-text">用户ID: {{ currentUser.id }}</text>
        <text class="info-text">用户类型: {{ currentUser.isVip ? 'VIP' : '普通' }}</text>
      </view>
      <button class="test-btn" @click="switchUser">
        切换测试用户
      </button>
      <button class="test-btn" @click="resetUser">
        重置为默认用户
      </button>
    </view>
  </view>
</template>

<script>
import { getReplyStyles, analyzeEmotion } from '../../api/emotion.js'
import { getHistoryList, getUserStats } from '../../api/history.js'
import { UserManager } from '../../utils/user.js'

export default {
  name: 'ApiTest',
  
  data() {
    return {
      testMessage: '今天心情不太好',
      stylesResult: '',
      emotionResult: '',
      historyResult: '',
      statsResult: '',
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      currentUser: {}
    }
  },

  onLoad() {
    this.loadCurrentUser()
  },
  
  methods: {
    // 加载当前用户信息
    loadCurrentUser() {
      this.currentUser = UserManager.getUserInfo() || UserManager.getDefaultUserInfo()
    },

    // 切换测试用户
    switchUser() {
      const testUsers = [
        {
          id: 1,
          username: 'testuser',
          nickname: '测试用户',
          email: '<EMAIL>',
          isVip: false,
          dailyQuota: 10
        },
        {
          id: 2,
          username: 'admin',
          nickname: '管理员',
          email: '<EMAIL>',
          isVip: true,
          dailyQuota: 100
        },
        {
          id: 3,
          username: 'vipuser',
          nickname: 'VIP用户',
          email: '<EMAIL>',
          isVip: true,
          dailyQuota: 50
        }
      ]

      uni.showActionSheet({
        itemList: testUsers.map(u => `${u.nickname} (${u.username})`),
        success: (res) => {
          const selectedUser = testUsers[res.tapIndex]
          UserManager.setUserInfo(selectedUser)
          this.loadCurrentUser()
          uni.showToast({
            title: `已切换到${selectedUser.nickname}`,
            icon: 'success'
          })
        }
      })
    },

    // 重置为默认用户
    resetUser() {
      UserManager.clearUserInfo()
      UserManager.initDefaultUser()
      this.loadCurrentUser()
      uni.showToast({
        title: '已重置为默认用户',
        icon: 'success'
      })
    },

    // 测试回复风格API
    async testReplyStyles() {
      this.loading1 = true
      try {
        const result = await getReplyStyles()
        this.stylesResult = JSON.stringify(result, null, 2)
        uni.showToast({
          title: '获取成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('测试回复风格API失败:', error)
        this.stylesResult = `错误: ${error.message || error}`
        uni.showToast({
          title: '获取失败',
          icon: 'none'
        })
      } finally {
        this.loading1 = false
      }
    },
    
    // 测试情感分析API
    async testEmotionAnalysis() {
      if (!this.testMessage.trim()) {
        uni.showToast({
          title: '请输入测试消息',
          icon: 'none'
        })
        return
      }
      
      this.loading2 = true
      try {
        const result = await analyzeEmotion(this.testMessage, ['warm_caring', 'humorous'], true)
        this.emotionResult = JSON.stringify(result, null, 2)
        uni.showToast({
          title: '分析成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('测试情感分析API失败:', error)
        this.emotionResult = `错误: ${error.message || error}`
        uni.showToast({
          title: '分析失败',
          icon: 'none'
        })
      } finally {
        this.loading2 = false
      }
    },
    
    // 测试历史记录API
    async testHistory() {
      this.loading3 = true
      try {
        const result = await getHistoryList(1, 5, 1)
        this.historyResult = JSON.stringify(result, null, 2)
        uni.showToast({
          title: '获取成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('测试历史记录API失败:', error)
        this.historyResult = `错误: ${error.message || error}`
        uni.showToast({
          title: '获取失败',
          icon: 'none'
        })
      } finally {
        this.loading3 = false
      }
    },
    
    // 测试用户统计API
    async testUserStats() {
      this.loading4 = true
      try {
        const result = await getUserStats(1)
        this.statsResult = JSON.stringify(result, null, 2)
        uni.showToast({
          title: '获取成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('测试用户统计API失败:', error)
        this.statsResult = `错误: ${error.message || error}`
        uni.showToast({
          title: '获取失败',
          icon: 'none'
        })
      } finally {
        this.loading4 = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.test-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .test-input {
    width: 100%;
    height: 80rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 8rpx;
    padding: 0 20rpx;
    margin-bottom: 20rpx;
    font-size: 28rpx;
  }
  
  .test-btn {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }
  
  .result {
    background: #f5f5f5;
    border-radius: 8rpx;
    padding: 20rpx;

    .result-title {
      display: block;
      font-size: 26rpx;
      font-weight: bold;
      color: #666;
      margin-bottom: 10rpx;
    }

    .result-content {
      font-size: 24rpx;
      color: #333;
      word-break: break-all;
      white-space: pre-wrap;
    }
  }

  .user-info {
    background: #e3f2fd;
    border-radius: 8rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;

    .info-text {
      display: block;
      font-size: 26rpx;
      color: #1976d2;
      margin-bottom: 8rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
