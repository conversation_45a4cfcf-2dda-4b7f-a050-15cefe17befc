package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.dto.request.UserLoginRequest;
import com.emotional.service.dto.response.UserLoginResponse;
import com.emotional.service.entity.User;
import com.emotional.service.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户管理", description = "用户登录、注册、信息管理等接口")
public class UserController {
    
    private final UserService userService;
    
    /**
     * 用户登录
     */
    @Operation(summary = "用户登录", description = "用户名密码登录")
    @PostMapping("/login")
    public Result<UserLoginResponse> login(
            @Valid @RequestBody UserLoginRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("用户登录请求: {}", request.getUsername());
        
        try {
            // 获取客户端信息
            String clientIp = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            // 执行登录
            UserLoginResponse response = userService.login(request, clientIp, userAgent);
            
            log.info("用户登录成功: {}", request.getUsername());
            
            return Result.success("登录成功", response);
            
        } catch (Exception e) {
            log.error("用户登录失败: {}", request.getUsername(), e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户信息
     */
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @GetMapping("/info/{userId}")
    public Result<User> getUserInfo(@PathVariable Long userId) {
        
        log.info("获取用户信息请求: {}", userId);
        
        try {
            User user = userService.getUserById(userId);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 清除敏感信息
            user.setPassword(null);
            
            return Result.success("获取成功", user);
            
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", userId, e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     */
    @Operation(summary = "更新用户信息", description = "更新用户基本信息")
    @PutMapping("/info/{userId}")
    public Result<String> updateUserInfo(
            @PathVariable Long userId,
            @RequestBody User userInfo) {
        
        log.info("更新用户信息请求: {}", userId);
        
        try {
            boolean success = userService.updateUserInfo(userId, userInfo);
            
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新用户信息失败: {}", userId, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 修改密码
     */
    @Operation(summary = "修改密码", description = "修改用户密码")
    @PutMapping("/password/{userId}")
    public Result<String> changePassword(
            @PathVariable Long userId,
            @RequestBody ChangePasswordRequest request) {
        
        log.info("修改密码请求: {}", userId);
        
        try {
            boolean success = userService.changePassword(userId, request.getOldPassword(), request.getNewPassword());
            
            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("原密码错误");
            }
            
        } catch (Exception e) {
            log.error("修改密码失败: {}", userId, e);
            return Result.error("修改失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 修改密码请求类
     */
    public static class ChangePasswordRequest {
        private String oldPassword;
        private String newPassword;
        
        public String getOldPassword() {
            return oldPassword;
        }
        
        public void setOldPassword(String oldPassword) {
            this.oldPassword = oldPassword;
        }
        
        public String getNewPassword() {
            return newPassword;
        }
        
        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
}
