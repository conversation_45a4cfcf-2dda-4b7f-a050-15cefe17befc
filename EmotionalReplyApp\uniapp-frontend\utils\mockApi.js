// 模拟API工具类
class MockApiUtil {
  // 延迟函数
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  // 模拟情感分析API
  static async analyzeEmotion(message) {
    // 模拟网络延迟
    await this.delay(1000)
    
    // 简单的情感分析逻辑
    const emotionKeywords = {
      '开心': ['开心', '高兴', '快乐', '兴奋', '哈哈', '😊', '😄', '🎉'],
      '难过': ['难过', '伤心', '痛苦', '失望', '😢', '😭', '💔'],
      '愤怒': ['生气', '愤怒', '气死', '讨厌', '😡', '🤬'],
      '担心': ['担心', '焦虑', '紧张', '害怕', '😰', '😟'],
      '兴奋': ['兴奋', '激动', '太棒了', 'amazing', '🤩', '✨'],
      '平静': ['平静', '还好', '一般', '普通', '😐'],
      '关心': ['关心', '照顾', '帮助', '支持', '❤️', '💕']
    }
    
    let detectedEmotion = '平静'
    let maxMatches = 0
    
    // 检测情感关键词
    for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
      const matches = keywords.filter(keyword => 
        message.toLowerCase().includes(keyword.toLowerCase())
      ).length
      
      if (matches > maxMatches) {
        maxMatches = matches
        detectedEmotion = emotion
      }
    }
    
    // 生成置信度
    const confidence = maxMatches > 0 
      ? Math.min(70 + maxMatches * 10, 95)
      : Math.floor(Math.random() * 30) + 60
    
    return {
      emotion: detectedEmotion,
      confidence
    }
  }
  
  // 模拟回复生成API
  static async generateReplies(message, emotion) {
    await this.delay(2000)
    
    // 根据不同情感生成不同的回复模板
    const replyTemplates = {
      '开心': [
        { style: 'warm_caring', content: '看到你这么开心，我也很高兴！继续保持这份好心情吧！' },
        { style: 'humorous', content: '哈哈，你的快乐都要溢出屏幕了！🎉 感染力十足啊！' },
        { style: 'rational', content: '很高兴看到你心情不错，这种积极的状态很棒，对身心健康都有好处。' },
        { style: 'concise', content: '真为你高兴！' },
        { style: 'romantic', content: '你的笑容是我见过最美的风景，愿你永远这么开心。💕' }
      ],
      '难过': [
        { style: 'warm_caring', content: '听起来你遇到了一些困难，我很关心你。如果需要帮助或者想聊聊，我随时都在。' },
        { style: 'humorous', content: '虽然现在有点难过，但记住，彩虹总在风雨后！🌈 而且我会陪着你等彩虹！' },
        { style: 'rational', content: '我理解你现在的感受。遇到问题时，我们可以一步步分析解决方案。你觉得最主要的问题是什么？' },
        { style: 'concise', content: '抱抱，会好起来的。' },
        { style: 'romantic', content: '亲爱的，无论发生什么，我都会陪在你身边。你的快乐就是我的快乐。💕' }
      ],
      '愤怒': [
        { style: 'warm_caring', content: '我能感受到你的愤怒，先深呼吸一下，我们慢慢聊。' },
        { style: 'humorous', content: '哇，感觉你现在像个小火龙！🔥 要不要我帮你灭灭火？' },
        { style: 'rational', content: '愤怒是正常的情绪反应，让我们冷静分析一下是什么让你这么生气。' },
        { style: 'concise', content: '先冷静一下。' },
        { style: 'romantic', content: '我知道你很生气，但在我心里你永远是最可爱的。💕' }
      ],
      '担心': [
        { style: 'warm_caring', content: '我能理解你的担心，有什么具体的事情让你焦虑吗？我们一起想办法。' },
        { style: 'humorous', content: '担心虫又来了吗？🐛 别怕，我来帮你赶走它们！' },
        { style: 'rational', content: '担心往往来自于对未知的恐惧，我们可以列出具体的担忧点，逐一分析解决。' },
        { style: 'concise', content: '别担心，有我在。' },
        { style: 'romantic', content: '不要害怕，我会保护你，陪你度过每一个难关。💕' }
      ],
      '兴奋': [
        { style: 'warm_caring', content: '看到你这么兴奋，我也被你的热情感染了！分享一下是什么好事吧！' },
        { style: 'humorous', content: '哇塞！你的兴奋度都要爆表了！🚀 快告诉我发生了什么！' },
        { style: 'rational', content: '你的兴奋很有感染力，看来遇到了很棒的事情。' },
        { style: 'concise', content: '太棒了！' },
        { style: 'romantic', content: '你兴奋的样子真是太迷人了，愿你永远保持这份热情。💕' }
      ],
      '平静': [
        { style: 'warm_caring', content: '你看起来很平静，这种状态很好。有什么想聊的吗？' },
        { style: 'humorous', content: '平静如水的你，是不是在思考人生的大问题？🤔' },
        { style: 'rational', content: '平静是一种很好的心理状态，有利于理性思考。' },
        { style: 'concise', content: '嗯，了解。' },
        { style: 'romantic', content: '你的平静让我感到安心，就像港湾一样温暖。💕' }
      ],
      '关心': [
        { style: 'warm_caring', content: '感受到了你的关心，真的很温暖。谢谢你这么体贴。' },
        { style: 'humorous', content: '哇，被关心的感觉真好！感觉自己像个小公主/小王子！👑' },
        { style: 'rational', content: '你的关心很珍贵，这种互相关爱的关系很美好。' },
        { style: 'concise', content: '谢谢关心。' },
        { style: 'romantic', content: '你的关心是我最珍贵的礼物，我也同样关心着你。💕' }
      ]
    }
    
    // 如果没有匹配的情感，使用默认回复
    const defaultReplies = [
      { style: 'warm_caring', content: '谢谢你的分享，我很关心你的感受。' },
      { style: 'humorous', content: '收到！让我想想怎么回复比较好... 🤔' },
      { style: 'rational', content: '我明白了，让我们来分析一下这个情况。' },
      { style: 'concise', content: '了解。' },
      { style: 'romantic', content: '无论什么时候，我都在这里陪着你。💕' }
    ]
    
    return replyTemplates[emotion] || defaultReplies
  }
  
  // 模拟用户统计数据
  static async getUserStats() {
    await this.delay(500)
    
    return {
      todayUsage: Math.floor(Math.random() * 8) + 1,
      totalUsage: Math.floor(Math.random() * 200) + 50,
      dailyQuota: 10,
      favoriteCount: Math.floor(Math.random() * 20) + 5,
      weeklyUsage: [2, 5, 3, 8, 6, 4, 7], // 最近7天的使用量
      emotionDistribution: {
        '开心': 25,
        '难过': 15,
        '平静': 30,
        '关心': 20,
        '其他': 10
      }
    }
  }
  
  // 模拟最近消息
  static getRecentMessages() {
    return [
      '今天心情不太好',
      '工作压力好大啊',
      '想你了',
      '晚上一起吃饭吗？',
      '谢谢你的帮助',
      '周末有什么计划？',
      '这个电影真好看',
      '天气真不错',
      '累死了',
      '开心死了！'
    ]
  }
}

export default MockApiUtil
