package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.entity.ReplyHistory;
import com.emotional.service.service.ReplyHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 历史记录控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/history")
@RequiredArgsConstructor
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
public class HistoryController {
    
    private final ReplyHistoryService replyHistoryService;
    
    /**
     * 获取用户历史记录
     */
    @GetMapping("/list")
    public Result<List<ReplyHistory>> getHistory(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(defaultValue = "1") Long userId) {
        
        try {
            List<ReplyHistory> historyList = replyHistoryService.getHistoryByUserId(userId, page, size);
            return Result.success("获取成功", historyList);
        } catch (Exception e) {
            log.error("获取历史记录失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户收藏的历史记录
     */
    @GetMapping("/favorites")
    public Result<List<ReplyHistory>> getFavorites(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(defaultValue = "1") Long userId) {
        
        try {
            List<ReplyHistory> favoriteList = replyHistoryService.getFavoriteHistoryByUserId(userId, page, size);
            return Result.success("获取成功", favoriteList);
        } catch (Exception e) {
            log.error("获取收藏记录失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 切换收藏状态
     */
    @PostMapping("/{historyId}/favorite")
    public Result<Boolean> toggleFavorite(
            @PathVariable @NotNull Long historyId,
            @RequestParam(defaultValue = "1") Long userId) {
        
        try {
            boolean success = replyHistoryService.toggleFavorite(historyId, userId);
            if (success) {
                return Result.success("操作成功", true);
            } else {
                return Result.error("操作失败，记录不存在或无权限");
            }
        } catch (Exception e) {
            log.error("切换收藏状态失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除历史记录
     */
    @DeleteMapping("/{historyId}")
    public Result<Boolean> deleteHistory(
            @PathVariable @NotNull Long historyId,
            @RequestParam(defaultValue = "1") Long userId) {
        
        try {
            boolean success = replyHistoryService.deleteHistory(historyId, userId);
            if (success) {
                return Result.success("删除成功", true);
            } else {
                return Result.error("删除失败，记录不存在或无权限");
            }
        } catch (Exception e) {
            log.error("删除历史记录失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 提交用户反馈
     */
    @PostMapping("/{historyId}/feedback")
    public Result<Boolean> submitFeedback(
            @PathVariable @NotNull Long historyId,
            @RequestParam(defaultValue = "1") Long userId,
            @RequestParam @Min(1) @Max(5) Integer rating,
            @RequestParam(required = false) String feedback) {
        
        try {
            boolean success = replyHistoryService.submitFeedback(historyId, userId, rating, feedback);
            if (success) {
                return Result.success("反馈提交成功", true);
            } else {
                return Result.error("提交失败，记录不存在或无权限");
            }
        } catch (Exception e) {
            log.error("提交反馈失败", e);
            return Result.error("提交失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户使用统计
     */
    @GetMapping("/stats")
    public Result<Object> getUserStats(@RequestParam(defaultValue = "1") Long userId) {
        try {
            int todayUsage = replyHistoryService.getTodayUsageCount(userId);
            int totalUsage = replyHistoryService.getTotalUsageCount(userId);
            
            return Result.success("获取成功", new Object() {
                public final int todayUsage = todayUsage;
                public final int totalUsage = totalUsage;
                public final int dailyQuota = 10; // 默认配额，后续从用户表获取
                public final int remainingQuota = Math.max(0, 10 - todayUsage);
            });
        } catch (Exception e) {
            log.error("获取用户统计失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
}
