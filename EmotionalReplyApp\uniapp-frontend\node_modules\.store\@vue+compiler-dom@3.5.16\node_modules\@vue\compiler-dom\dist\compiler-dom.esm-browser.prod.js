/**
* @vue/compiler-dom v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/let e;function t(e){let t=Object.create(null);for(let n of e.split(","))t[n]=1;return e=>e in t}let n={},i=()=>{},r=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||97>e.charCodeAt(2)),o=Object.assign,a=Array.isArray,l=e=>"string"==typeof e,c=e=>"symbol"==typeof e,h=e=>null!==e&&"object"==typeof e,d=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),p=t("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),u=e=>{let t=Object.create(null);return n=>t[n]||(t[n]=e(n))},f=/-(\w)/g,E=u(e=>e.replace(f,(e,t)=>t?t.toUpperCase():"")),_=u(e=>e.charAt(0).toUpperCase()+e.slice(1)),m=u(e=>e?`on${_(e)}`:"");function S(e,t=0,n=e.length){if((t=Math.max(0,Math.min(t,e.length)))>(n=Math.max(0,Math.min(n,e.length))))return"";let i=e.split(/(\r?\n)/),r=i.filter((e,t)=>t%2==1);i=i.filter((e,t)=>t%2==0);let s=0,o=[];for(let e=0;e<i.length;e++)if((s+=i[e].length+(r[e]&&r[e].length||0))>=t){for(let a=e-2;a<=e+2||n>s;a++){if(a<0||a>=i.length)continue;let l=a+1;o.push(`${l}${" ".repeat(Math.max(3-String(l).length,0))}|  ${i[a]}`);let c=i[a].length,h=r[a]&&r[a].length||0;if(a===e){let e=t-(s-(c+h)),i=Math.max(1,n>s?c-e:n-t);o.push("   |  "+" ".repeat(e)+"^".repeat(i))}else if(a>e){if(n>s){let e=Math.max(Math.min(n-s,c),1);o.push("   |  "+"^".repeat(e))}s+=c+h}}break}return o.join(`
`)}let g=/;(?![^(]*\))/g,T=/:([^]+)/,N=/\/\*[^]*?\*\//g,I=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),y=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),O=t("annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics"),A=t("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),C=Symbol(""),b=Symbol(""),v=Symbol(""),R=Symbol(""),x=Symbol(""),L=Symbol(""),M=Symbol(""),P=Symbol(""),D=Symbol(""),V=Symbol(""),k=Symbol(""),X=Symbol(""),w=Symbol(""),U=Symbol(""),F=Symbol(""),B=Symbol(""),$=Symbol(""),H=Symbol(""),G=Symbol(""),q=Symbol(""),J=Symbol(""),j=Symbol(""),W=Symbol(""),K=Symbol(""),Y=Symbol(""),Q=Symbol(""),z=Symbol(""),Z=Symbol(""),ee=Symbol(""),et=Symbol(""),en=Symbol(""),ei=Symbol(""),er=Symbol(""),es=Symbol(""),eo=Symbol(""),ea=Symbol(""),el=Symbol(""),ec=Symbol(""),eh=Symbol(""),ed={[C]:"Fragment",[b]:"Teleport",[v]:"Suspense",[R]:"KeepAlive",[x]:"BaseTransition",[L]:"openBlock",[M]:"createBlock",[P]:"createElementBlock",[D]:"createVNode",[V]:"createElementVNode",[k]:"createCommentVNode",[X]:"createTextVNode",[w]:"createStaticVNode",[U]:"resolveComponent",[F]:"resolveDynamicComponent",[B]:"resolveDirective",[$]:"resolveFilter",[H]:"withDirectives",[G]:"renderList",[q]:"renderSlot",[J]:"createSlots",[j]:"toDisplayString",[W]:"mergeProps",[K]:"normalizeClass",[Y]:"normalizeStyle",[Q]:"normalizeProps",[z]:"guardReactiveProps",[Z]:"toHandlers",[ee]:"camelize",[et]:"capitalize",[en]:"toHandlerKey",[ei]:"setBlockTracking",[er]:"pushScopeId",[es]:"popScopeId",[eo]:"withCtx",[ea]:"unref",[el]:"isRef",[ec]:"withMemo",[eh]:"isMemoSame"};function ep(e){Object.getOwnPropertySymbols(e).forEach(t=>{ed[t]=e[t]})}let eu={HTML:0,0:"HTML",SVG:1,1:"SVG",MATH_ML:2,2:"MATH_ML"},ef={ROOT:0,0:"ROOT",ELEMENT:1,1:"ELEMENT",TEXT:2,2:"TEXT",COMMENT:3,3:"COMMENT",SIMPLE_EXPRESSION:4,4:"SIMPLE_EXPRESSION",INTERPOLATION:5,5:"INTERPOLATION",ATTRIBUTE:6,6:"ATTRIBUTE",DIRECTIVE:7,7:"DIRECTIVE",COMPOUND_EXPRESSION:8,8:"COMPOUND_EXPRESSION",IF:9,9:"IF",IF_BRANCH:10,10:"IF_BRANCH",FOR:11,11:"FOR",TEXT_CALL:12,12:"TEXT_CALL",VNODE_CALL:13,13:"VNODE_CALL",JS_CALL_EXPRESSION:14,14:"JS_CALL_EXPRESSION",JS_OBJECT_EXPRESSION:15,15:"JS_OBJECT_EXPRESSION",JS_PROPERTY:16,16:"JS_PROPERTY",JS_ARRAY_EXPRESSION:17,17:"JS_ARRAY_EXPRESSION",JS_FUNCTION_EXPRESSION:18,18:"JS_FUNCTION_EXPRESSION",JS_CONDITIONAL_EXPRESSION:19,19:"JS_CONDITIONAL_EXPRESSION",JS_CACHE_EXPRESSION:20,20:"JS_CACHE_EXPRESSION",JS_BLOCK_STATEMENT:21,21:"JS_BLOCK_STATEMENT",JS_TEMPLATE_LITERAL:22,22:"JS_TEMPLATE_LITERAL",JS_IF_STATEMENT:23,23:"JS_IF_STATEMENT",JS_ASSIGNMENT_EXPRESSION:24,24:"JS_ASSIGNMENT_EXPRESSION",JS_SEQUENCE_EXPRESSION:25,25:"JS_SEQUENCE_EXPRESSION",JS_RETURN_STATEMENT:26,26:"JS_RETURN_STATEMENT"},eE={ELEMENT:0,0:"ELEMENT",COMPONENT:1,1:"COMPONENT",SLOT:2,2:"SLOT",TEMPLATE:3,3:"TEMPLATE"},e_={NOT_CONSTANT:0,0:"NOT_CONSTANT",CAN_SKIP_PATCH:1,1:"CAN_SKIP_PATCH",CAN_CACHE:2,2:"CAN_CACHE",CAN_STRINGIFY:3,3:"CAN_STRINGIFY"},em={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0},source:""};function eS(e,t=""){return{type:0,source:t,children:e,helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:[],temps:0,codegenNode:void 0,loc:em}}function eg(e,t,n,i,r,s,o,a=!1,l=!1,c=!1,h=em){return e&&(a?(e.helper(L),e.helper(eX(e.inSSR,c))):e.helper(ek(e.inSSR,c)),o&&e.helper(H)),{type:13,tag:t,props:n,children:i,patchFlag:r,dynamicProps:s,directives:o,isBlock:a,disableTracking:l,isComponent:c,loc:h}}function eT(e,t=em){return{type:17,loc:t,elements:e}}function eN(e,t=em){return{type:15,loc:t,properties:e}}function eI(e,t){return{type:16,loc:em,key:l(e)?ey(e,!0):e,value:t}}function ey(e,t=!1,n=em,i=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:i}}function eO(e,t){return{type:5,loc:t,content:l(e)?ey(e,!1,t):e}}function eA(e,t=em){return{type:8,loc:t,children:e}}function eC(e,t=[],n=em){return{type:14,loc:n,callee:e,arguments:t}}function eb(e,t,n=!1,i=!1,r=em){return{type:18,params:e,returns:t,newline:n,isSlot:i,loc:r}}function ev(e,t,n,i=!0){return{type:19,test:e,consequent:t,alternate:n,newline:i,loc:em}}function eR(e,t,n=!1,i=!1){return{type:20,index:e,value:t,needPauseTracking:n,inVOnce:i,needArraySpread:!1,loc:em}}function ex(e){return{type:21,body:e,loc:em}}function eL(e){return{type:22,elements:e,loc:em}}function eM(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:em}}function eP(e,t){return{type:24,left:e,right:t,loc:em}}function eD(e){return{type:25,expressions:e,loc:em}}function eV(e){return{type:26,returns:e,loc:em}}function ek(e,t){return e||t?D:V}function eX(e,t){return e||t?M:P}function ew(e,{helper:t,removeHelper:n,inSSR:i}){e.isBlock||(e.isBlock=!0,n(ek(i,e.isComponent)),t(L),t(eX(i,e.isComponent)))}let eU=new Uint8Array([123,123]),eF=new Uint8Array([125,125]);function eB(e){return e>=97&&e<=122||e>=65&&e<=90}function e$(e){return 32===e||10===e||9===e||12===e||13===e}function eH(e){return 47===e||62===e||e$(e)}function eG(e){let t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}let eq={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97])},eJ={COMPILER_IS_ON_ELEMENT:"COMPILER_IS_ON_ELEMENT",COMPILER_V_BIND_SYNC:"COMPILER_V_BIND_SYNC",COMPILER_V_BIND_OBJECT_ORDER:"COMPILER_V_BIND_OBJECT_ORDER",COMPILER_V_ON_NATIVE:"COMPILER_V_ON_NATIVE",COMPILER_V_IF_V_FOR_PRECEDENCE:"COMPILER_V_IF_V_FOR_PRECEDENCE",COMPILER_NATIVE_TEMPLATE:"COMPILER_NATIVE_TEMPLATE",COMPILER_INLINE_TEMPLATE:"COMPILER_INLINE_TEMPLATE",COMPILER_FILTERS:"COMPILER_FILTERS"},ej={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3-migration.vuejs.org/breaking-changes/v-model.html"},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3-migration.vuejs.org/breaking-changes/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html"},COMPILER_FILTERS:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3-migration.vuejs.org/breaking-changes/filters.html"}};function eW(e,{compatConfig:t}){let n=t&&t[e];return"MODE"===e?n||3:n}function eK(e,t){let n=eW("MODE",t),i=eW(e,t);return 3===n?!0===i:!1!==i}function eY(e,t,n,...i){return eK(e,t)}function eQ(e,t,n,...i){if("suppress-warning"===eW(e,t))return;let{message:r,link:s}=ej[e],o=SyntaxError(`(deprecation ${e}) ${"function"==typeof r?r(...i):r}${s?`
  Details: ${s}`:""}`);o.code=e,n&&(o.loc=n),t.onWarn(o)}function ez(e){throw e}function eZ(e){}function e1(e,t,n,i){let r=SyntaxError(String(`https://vuejs.org/error-reference/#compiler-${e}`));return r.code=e,r.loc=t,r}let e0={ABRUPT_CLOSING_OF_EMPTY_COMMENT:0,0:"ABRUPT_CLOSING_OF_EMPTY_COMMENT",CDATA_IN_HTML_CONTENT:1,1:"CDATA_IN_HTML_CONTENT",DUPLICATE_ATTRIBUTE:2,2:"DUPLICATE_ATTRIBUTE",END_TAG_WITH_ATTRIBUTES:3,3:"END_TAG_WITH_ATTRIBUTES",END_TAG_WITH_TRAILING_SOLIDUS:4,4:"END_TAG_WITH_TRAILING_SOLIDUS",EOF_BEFORE_TAG_NAME:5,5:"EOF_BEFORE_TAG_NAME",EOF_IN_CDATA:6,6:"EOF_IN_CDATA",EOF_IN_COMMENT:7,7:"EOF_IN_COMMENT",EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT:8,8:"EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT",EOF_IN_TAG:9,9:"EOF_IN_TAG",INCORRECTLY_CLOSED_COMMENT:10,10:"INCORRECTLY_CLOSED_COMMENT",INCORRECTLY_OPENED_COMMENT:11,11:"INCORRECTLY_OPENED_COMMENT",INVALID_FIRST_CHARACTER_OF_TAG_NAME:12,12:"INVALID_FIRST_CHARACTER_OF_TAG_NAME",MISSING_ATTRIBUTE_VALUE:13,13:"MISSING_ATTRIBUTE_VALUE",MISSING_END_TAG_NAME:14,14:"MISSING_END_TAG_NAME",MISSING_WHITESPACE_BETWEEN_ATTRIBUTES:15,15:"MISSING_WHITESPACE_BETWEEN_ATTRIBUTES",NESTED_COMMENT:16,16:"NESTED_COMMENT",UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME:17,17:"UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME",UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE:18,18:"UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE",UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME:19,19:"UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME",UNEXPECTED_NULL_CHARACTER:20,20:"UNEXPECTED_NULL_CHARACTER",UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME:21,21:"UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME",UNEXPECTED_SOLIDUS_IN_TAG:22,22:"UNEXPECTED_SOLIDUS_IN_TAG",X_INVALID_END_TAG:23,23:"X_INVALID_END_TAG",X_MISSING_END_TAG:24,24:"X_MISSING_END_TAG",X_MISSING_INTERPOLATION_END:25,25:"X_MISSING_INTERPOLATION_END",X_MISSING_DIRECTIVE_NAME:26,26:"X_MISSING_DIRECTIVE_NAME",X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END:27,27:"X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END",X_V_IF_NO_EXPRESSION:28,28:"X_V_IF_NO_EXPRESSION",X_V_IF_SAME_KEY:29,29:"X_V_IF_SAME_KEY",X_V_ELSE_NO_ADJACENT_IF:30,30:"X_V_ELSE_NO_ADJACENT_IF",X_V_FOR_NO_EXPRESSION:31,31:"X_V_FOR_NO_EXPRESSION",X_V_FOR_MALFORMED_EXPRESSION:32,32:"X_V_FOR_MALFORMED_EXPRESSION",X_V_FOR_TEMPLATE_KEY_PLACEMENT:33,33:"X_V_FOR_TEMPLATE_KEY_PLACEMENT",X_V_BIND_NO_EXPRESSION:34,34:"X_V_BIND_NO_EXPRESSION",X_V_ON_NO_EXPRESSION:35,35:"X_V_ON_NO_EXPRESSION",X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET:36,36:"X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET",X_V_SLOT_MIXED_SLOT_USAGE:37,37:"X_V_SLOT_MIXED_SLOT_USAGE",X_V_SLOT_DUPLICATE_SLOT_NAMES:38,38:"X_V_SLOT_DUPLICATE_SLOT_NAMES",X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN:39,39:"X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN",X_V_SLOT_MISPLACED:40,40:"X_V_SLOT_MISPLACED",X_V_MODEL_NO_EXPRESSION:41,41:"X_V_MODEL_NO_EXPRESSION",X_V_MODEL_MALFORMED_EXPRESSION:42,42:"X_V_MODEL_MALFORMED_EXPRESSION",X_V_MODEL_ON_SCOPE_VARIABLE:43,43:"X_V_MODEL_ON_SCOPE_VARIABLE",X_V_MODEL_ON_PROPS:44,44:"X_V_MODEL_ON_PROPS",X_INVALID_EXPRESSION:45,45:"X_INVALID_EXPRESSION",X_KEEP_ALIVE_INVALID_CHILDREN:46,46:"X_KEEP_ALIVE_INVALID_CHILDREN",X_PREFIX_ID_NOT_SUPPORTED:47,47:"X_PREFIX_ID_NOT_SUPPORTED",X_MODULE_MODE_NOT_SUPPORTED:48,48:"X_MODULE_MODE_NOT_SUPPORTED",X_CACHE_HANDLER_NOT_SUPPORTED:49,49:"X_CACHE_HANDLER_NOT_SUPPORTED",X_SCOPE_ID_NOT_SUPPORTED:50,50:"X_SCOPE_ID_NOT_SUPPORTED",X_VNODE_HOOKS:51,51:"X_VNODE_HOOKS",X_V_BIND_INVALID_SAME_NAME_ARGUMENT:52,52:"X_V_BIND_INVALID_SAME_NAME_ARGUMENT",__EXTEND_POINT__:53,53:"__EXTEND_POINT__"},e2={0:"Illegal comment.",1:"CDATA section is allowed only in XML context.",2:"Duplicate attribute.",3:"End tag cannot have attributes.",4:"Illegal '/' in tags.",5:"Unexpected EOF in tag.",6:"Unexpected EOF in CDATA section.",7:"Unexpected EOF in comment.",8:"Unexpected EOF in script.",9:"Unexpected EOF in tag.",10:"Incorrectly closed comment.",11:"Incorrectly opened comment.",12:"Illegal tag name. Use '&lt;' to print '<'.",13:"Attribute value was expected.",14:"End tag name was expected.",15:"Whitespace was expected.",16:"Unexpected '\x3c!--' in comment.",17:"Attribute name cannot contain U+0022 (\"), U+0027 ('), and U+003C (<).",18:"Unquoted attribute value cannot contain U+0022 (\"), U+0027 ('), U+003C (<), U+003D (=), and U+0060 (`).",19:"Attribute name cannot start with '='.",21:"'<?' is allowed only in XML context.",20:"Unexpected null character.",22:"Illegal '/' in tags.",23:"Invalid end tag.",24:"Element is missing end tag.",25:"Interpolation end sign was not found.",27:"End bracket for dynamic directive argument was not found. Note that dynamic directive argument cannot contain spaces.",26:"Legal directive name was expected.",28:"v-if/v-else-if is missing expression.",29:"v-if/else branches must use unique keys.",30:"v-else/v-else-if has no adjacent v-if or v-else-if.",31:"v-for is missing expression.",32:"v-for has invalid expression.",33:"<template v-for> key should be placed on the <template> tag.",34:"v-bind is missing expression.",52:"v-bind with same-name shorthand only allows static argument.",35:"v-on is missing expression.",36:"Unexpected custom directive on <slot> outlet.",37:"Mixed v-slot usage on both the component and nested <template>. When there are multiple named slots, all slots should use <template> syntax to avoid scope ambiguity.",38:"Duplicate slot names found. ",39:"Extraneous children found when component already has explicitly named default slot. These children will be ignored.",40:"v-slot can only be used on components or <template> tags.",41:"v-model is missing expression.",42:"v-model value must be a valid JavaScript member expression.",43:"v-model cannot be used on v-for or v-slot scope variables because they are not writable.",44:`v-model cannot be used on a prop, because local prop bindings are not writable.
Use a v-bind binding combined with a v-on listener that emits update:x event instead.`,45:"Error parsing JavaScript expression: ",46:"<KeepAlive> expects exactly one child component.",51:"@vnode-* hooks in templates are no longer supported. Use the vue: prefix instead. For example, @vnode-mounted should be changed to @vue:mounted. @vnode-* hooks support has been removed in 3.4.",47:'"prefixIdentifiers" option is not supported in this build of compiler.',48:"ES module mode is not supported in this build of compiler.",49:'"cacheHandlers" option is only supported when the "prefixIdentifiers" option is enabled.',50:'"scopeId" option is only supported in module mode.',53:""};function e3(e,t,n=!1,i=[],r=Object.create(null)){}function e4(e,t,n){return!1}function e6(e,t){if(e&&("ObjectProperty"===e.type||"ArrayPattern"===e.type)){let e=t.length;for(;e--;){let n=t[e];if("AssignmentExpression"===n.type)return!0;if("ObjectProperty"!==n.type&&!n.type.endsWith("Pattern"))break}}return!1}function e5(e){let t=e.length;for(;t--;){let n=e[t];if("NewExpression"===n.type)return!0;if("MemberExpression"!==n.type)break}return!1}function e9(e,t){for(let n of e.params)for(let e of e8(n))t(e)}function e7(e,t){for(let i of e.body)if("VariableDeclaration"===i.type){if(i.declare)continue;for(let e of i.declarations)for(let n of e8(e.id))t(n)}else if("FunctionDeclaration"===i.type||"ClassDeclaration"===i.type){if(i.declare||!i.id)continue;t(i.id)}else{var n;("ForOfStatement"===(n=i).type||"ForInStatement"===n.type||"ForStatement"===n.type)&&function(e,t,n){let i="ForStatement"===e.type?e.init:e.left;if(i&&"VariableDeclaration"===i.type&&"var"===i.kind&&t)for(let e of i.declarations)for(let t of e8(e.id))n(t)}(i,!0,t)}}function e8(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;"MemberExpression"===n.type;)n=n.object;t.push(n);break;case"ObjectPattern":for(let n of e.properties)"RestElement"===n.type?e8(n.argument,t):e8(n.value,t);break;case"ArrayPattern":e.elements.forEach(e=>{e&&e8(e,t)});break;case"RestElement":e8(e.argument,t);break;case"AssignmentPattern":e8(e.left,t)}return t}let te=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),tt=e=>e&&("ObjectProperty"===e.type||"ObjectMethod"===e.type)&&!e.computed,tn=(e,t)=>tt(t)&&t.key===e,ti=["TSAsExpression","TSTypeAssertion","TSNonNullExpression","TSInstantiationExpression","TSSatisfiesExpression"];function tr(e){return ti.includes(e.type)?tr(e.expression):e}let ts=e=>4===e.type&&e.isStatic;function to(e){switch(e){case"Teleport":case"teleport":return b;case"Suspense":case"suspense":return v;case"KeepAlive":case"keep-alive":return R;case"BaseTransition":case"base-transition":return x}}let ta=/^\d|[^\$\w\xA0-\uFFFF]/,tl=e=>!ta.test(e),tc=/[A-Za-z_$\xA0-\uFFFF]/,th=/[\.\?\w$\xA0-\uFFFF]/,td=/\s+[.[]\s*|\s*[.[]\s+/g,tp=e=>4===e.type?e.content:e.loc.source,tu=e=>{let t=tp(e).trim().replace(td,e=>e.trim()),n=0,i=[],r=0,s=0,o=null;for(let e=0;e<t.length;e++){let a=t.charAt(e);switch(n){case 0:if("["===a)i.push(n),n=1,r++;else if("("===a)i.push(n),n=2,s++;else if(!(0===e?tc:th).test(a))return!1;break;case 1:"'"===a||'"'===a||"`"===a?(i.push(n),n=3,o=a):"["===a?r++:"]"!==a||--r||(n=i.pop());break;case 2:if("'"===a||'"'===a||"`"===a)i.push(n),n=3,o=a;else if("("===a)s++;else if(")"===a){if(e===t.length-1)return!1;--s||(n=i.pop())}break;case 3:a===o&&(n=i.pop(),o=null)}}return!r&&!s},tf=i,tE=tu,t_=/^\s*(async\s*)?(\([^)]*?\)|[\w$_]+)\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,tm=e=>t_.test(tp(e)),tS=i,tg=tm;function tT(e,t,n=t.length){return tN({offset:e.offset,line:e.line,column:e.column},t,n)}function tN(e,t,n=t.length){let i=0,r=-1;for(let e=0;e<n;e++)10===t.charCodeAt(e)&&(i++,r=e);return e.offset+=n,e.line+=i,e.column=-1===r?e.column+n:n-r,e}function tI(e,t){if(!e)throw Error(t||"unexpected compiler condition")}function ty(e,t,n=!1){for(let i=0;i<e.props.length;i++){let r=e.props[i];if(7===r.type&&(n||r.exp)&&(l(t)?r.name===t:t.test(r.name)))return r}}function tO(e,t,n=!1,i=!1){for(let r=0;r<e.props.length;r++){let s=e.props[r];if(6===s.type){if(n)continue;if(s.name===t&&(s.value||i))return s}else if("bind"===s.name&&(s.exp||i)&&tA(s.arg,t))return s}}function tA(e,t){return!!(e&&ts(e)&&e.content===t)}function tC(e){return e.props.some(e=>7===e.type&&"bind"===e.name&&(!e.arg||4!==e.arg.type||!e.arg.isStatic))}function tb(e){return 5===e.type||2===e.type}function tv(e){return 7===e.type&&"slot"===e.name}function tR(e){return 1===e.type&&3===e.tagType}function tx(e){return 1===e.type&&2===e.tagType}let tL=new Set([Q,z]);function tM(e,t,n){let i,r,s=13===e.type?e.props:e.arguments[2],o=[];if(s&&!l(s)&&14===s.type){let e=function e(t,n=[]){if(t&&!l(t)&&14===t.type){let i=t.callee;if(!l(i)&&tL.has(i))return e(t.arguments[0],n.concat(t))}return[t,n]}(s);s=e[0],r=(o=e[1])[o.length-1]}if(null==s||l(s))i=eN([t]);else if(14===s.type){let e=s.arguments[0];l(e)||15!==e.type?s.callee===Z?i=eC(n.helper(W),[eN([t]),s]):s.arguments.unshift(eN([t])):tP(t,e)||e.properties.unshift(t),i||(i=s)}else 15===s.type?(tP(t,s)||s.properties.unshift(t),i=s):(i=eC(n.helper(W),[eN([t]),s]),r&&r.callee===z&&(r=o[o.length-2]));13===e.type?r?r.arguments[0]=i:e.props=i:r?r.arguments[0]=i:e.arguments[2]=i}function tP(e,t){let n=!1;if(4===e.key.type){let i=e.key.content;n=t.properties.some(e=>4===e.key.type&&e.key.content===i)}return n}function tD(e,t){return`_${t}_${e.replace(/[^\w]/g,(t,n)=>"-"===t?"_":e.charCodeAt(n).toString())}`}function tV(e,t){if(!e||0===Object.keys(t).length)return!1;switch(e.type){case 1:for(let n=0;n<e.props.length;n++){let i=e.props[n];if(7===i.type&&(tV(i.arg,t)||tV(i.exp,t)))return!0}return e.children.some(e=>tV(e,t));case 11:if(tV(e.source,t))return!0;return e.children.some(e=>tV(e,t));case 9:return e.branches.some(e=>tV(e,t));case 10:if(tV(e.condition,t))return!0;return e.children.some(e=>tV(e,t));case 4:return!e.isStatic&&tl(e.content)&&!!t[e.content];case 8:return e.children.some(e=>h(e)&&tV(e,t));case 5:case 12:return tV(e.content,t);default:return!1}}function tk(e){return 14===e.type&&e.callee===ec?e.arguments[1].returns:e}let tX=/([\s\S]*?)\s+(?:in|of)\s+(\S[\s\S]*)/,tw={parseMode:"base",ns:0,delimiters:["{{","}}"],getNamespace:()=>0,isVoidTag:r,isPreTag:r,isIgnoreNewlineTag:r,isCustomElement:r,onError:ez,onWarn:eZ,comments:!1,prefixIdentifiers:!1},tU=tw,tF=null,tB="",t$=null,tH=null,tG="",tq=-1,tJ=-1,tj=0,tW=!1,tK=null,tY=[],tQ=new class{constructor(e,t){this.stack=e,this.cbs=t,this.state=1,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=1,this.inRCDATA=!1,this.inXML=!1,this.inVPre=!1,this.newlines=[],this.mode=0,this.delimiterOpen=eU,this.delimiterClose=eF,this.delimiterIndex=-1,this.currentSequence=void 0,this.sequenceIndex=0}get inSFCRoot(){return 2===this.mode&&0===this.stack.length}reset(){this.state=1,this.mode=0,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=1,this.inRCDATA=!1,this.currentSequence=void 0,this.newlines.length=0,this.delimiterOpen=eU,this.delimiterClose=eF}getPos(e){let t=1,n=e+1;for(let i=this.newlines.length-1;i>=0;i--){let r=this.newlines[i];if(e>r){t=i+2,n=e-r;break}}return{column:n,line:t,offset:e}}peek(){return this.buffer.charCodeAt(this.index+1)}stateText(e){60===e?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=5,this.sectionStart=this.index):this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e))}stateInterpolationOpen(e){if(e===this.delimiterOpen[this.delimiterIndex])if(this.delimiterIndex===this.delimiterOpen.length-1){let e=this.index+1-this.delimiterOpen.length;e>this.sectionStart&&this.cbs.ontext(this.sectionStart,e),this.state=3,this.sectionStart=e}else this.delimiterIndex++;else this.inRCDATA?(this.state=32,this.stateInRCDATA(e)):(this.state=1,this.stateText(e))}stateInterpolation(e){e===this.delimiterClose[0]&&(this.state=4,this.delimiterIndex=0,this.stateInterpolationClose(e))}stateInterpolationClose(e){e===this.delimiterClose[this.delimiterIndex]?this.delimiterIndex===this.delimiterClose.length-1?(this.cbs.oninterpolation(this.sectionStart,this.index+1),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):this.delimiterIndex++:(this.state=3,this.stateInterpolation(e))}stateSpecialStartSequence(e){let t=this.sequenceIndex===this.currentSequence.length;if(t?eH(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.inRCDATA=!1;this.sequenceIndex=0,this.state=6,this.stateInTagName(e)}stateInRCDATA(e){if(this.sequenceIndex===this.currentSequence.length){if(62===e||e$(e)){let t=this.index-this.currentSequence.length;if(this.sectionStart<t){let e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}this.sectionStart=t+2,this.stateInClosingTagName(e),this.inRCDATA=!1;return}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence!==eq.TitleEnd&&(this.currentSequence!==eq.TextareaEnd||this.inSFCRoot)?this.fastForwardTo(60)&&(this.sequenceIndex=1):this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e)):this.sequenceIndex=Number(60===e)}stateCDATASequence(e){e===eq.Cdata[this.sequenceIndex]?++this.sequenceIndex===eq.Cdata.length&&(this.state=28,this.currentSequence=eq.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=23,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length;){let t=this.buffer.charCodeAt(this.index);if(10===t&&this.newlines.push(this.index),t===e)return!0}return this.index=this.buffer.length-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===eq.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index-2):this.cbs.oncomment(this.sectionStart,this.index-2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=1):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}startSpecial(e,t){this.enterRCDATA(e,t),this.state=31}enterRCDATA(e,t){this.inRCDATA=!0,this.currentSequence=e,this.sequenceIndex=t}stateBeforeTagName(e){33===e?(this.state=22,this.sectionStart=this.index+1):63===e?(this.state=24,this.sectionStart=this.index+1):eB(e)?(this.sectionStart=this.index,0===this.mode?this.state=6:this.inSFCRoot?this.state=34:this.inXML?this.state=6:116===e?this.state=30:this.state=115===e?29:6):47===e?this.state=8:(this.state=1,this.stateText(e))}stateInTagName(e){eH(e)&&this.handleTagName(e)}stateInSFCRootTagName(e){if(eH(e)){let t=this.buffer.slice(this.sectionStart,this.index);"template"!==t&&this.enterRCDATA(eG("</"+t),0),this.handleTagName(e)}}handleTagName(e){this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)}stateBeforeClosingTagName(e){e$(e)||(62===e?(this.state=1,this.sectionStart=this.index+1):(this.state=eB(e)?9:27,this.sectionStart=this.index))}stateInClosingTagName(e){(62===e||e$(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=10,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){62===e&&(this.state=1,this.sectionStart=this.index+1)}stateBeforeAttrName(e){62===e?(this.cbs.onopentagend(this.index),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):47===e?this.state=7:60===e&&47===this.peek()?(this.cbs.onopentagend(this.index),this.state=5,this.sectionStart=this.index):e$(e)||this.handleAttrStart(e)}handleAttrStart(e){118===e&&45===this.peek()?(this.state=13,this.sectionStart=this.index):46===e||58===e||64===e||35===e?(this.cbs.ondirname(this.index,this.index+1),this.state=14,this.sectionStart=this.index+1):(this.state=12,this.sectionStart=this.index)}stateInSelfClosingTag(e){62===e?(this.cbs.onselfclosingtag(this.index),this.state=1,this.sectionStart=this.index+1,this.inRCDATA=!1):e$(e)||(this.state=11,this.stateBeforeAttrName(e))}stateInAttrName(e){(61===e||eH(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.handleAttrNameEnd(e))}stateInDirName(e){61===e||eH(e)?(this.cbs.ondirname(this.sectionStart,this.index),this.handleAttrNameEnd(e)):58===e?(this.cbs.ondirname(this.sectionStart,this.index),this.state=14,this.sectionStart=this.index+1):46===e&&(this.cbs.ondirname(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDirArg(e){61===e||eH(e)?(this.cbs.ondirarg(this.sectionStart,this.index),this.handleAttrNameEnd(e)):91===e?this.state=15:46===e&&(this.cbs.ondirarg(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDynamicDirArg(e){93===e?this.state=14:(61===e||eH(e))&&(this.cbs.ondirarg(this.sectionStart,this.index+1),this.handleAttrNameEnd(e))}stateInDirModifier(e){61===e||eH(e)?(this.cbs.ondirmodifier(this.sectionStart,this.index),this.handleAttrNameEnd(e)):46===e&&(this.cbs.ondirmodifier(this.sectionStart,this.index),this.sectionStart=this.index+1)}handleAttrNameEnd(e){this.sectionStart=this.index,this.state=17,this.cbs.onattribnameend(this.index),this.stateAfterAttrName(e)}stateAfterAttrName(e){61===e?this.state=18:47===e||62===e?(this.cbs.onattribend(0,this.sectionStart),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)):e$(e)||(this.cbs.onattribend(0,this.sectionStart),this.handleAttrStart(e))}stateBeforeAttrValue(e){34===e?(this.state=19,this.sectionStart=this.index+1):39===e?(this.state=20,this.sectionStart=this.index+1):e$(e)||(this.sectionStart=this.index,this.state=21,this.stateInAttrValueNoQuotes(e))}handleInAttrValue(e,t){(e===t||this.fastForwardTo(t))&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(34===t?3:2,this.index+1),this.state=11)}stateInAttrValueDoubleQuotes(e){this.handleInAttrValue(e,34)}stateInAttrValueSingleQuotes(e){this.handleInAttrValue(e,39)}stateInAttrValueNoQuotes(e){e$(e)||62===e?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(1,this.index),this.state=11,this.stateBeforeAttrName(e)):(39===e||60===e||61===e||96===e)&&this.cbs.onerr(18,this.index)}stateBeforeDeclaration(e){91===e?(this.state=26,this.sequenceIndex=0):this.state=45===e?25:23}stateInDeclaration(e){(62===e||this.fastForwardTo(62))&&(this.state=1,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(62===e||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeComment(e){45===e?(this.state=28,this.currentSequence=eq.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=23}stateInSpecialComment(e){(62===e||this.fastForwardTo(62))&&(this.cbs.oncomment(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){e===eq.ScriptEnd[3]?this.startSpecial(eq.ScriptEnd,4):e===eq.StyleEnd[3]?this.startSpecial(eq.StyleEnd,4):(this.state=6,this.stateInTagName(e))}stateBeforeSpecialT(e){e===eq.TitleEnd[3]?this.startSpecial(eq.TitleEnd,4):e===eq.TextareaEnd[3]?this.startSpecial(eq.TextareaEnd,4):(this.state=6,this.stateInTagName(e))}startEntity(){}stateInEntity(){}parse(e){for(this.buffer=e;this.index<this.buffer.length;){let e=this.buffer.charCodeAt(this.index);switch(10===e&&this.newlines.push(this.index),this.state){case 1:this.stateText(e);break;case 2:this.stateInterpolationOpen(e);break;case 3:this.stateInterpolation(e);break;case 4:this.stateInterpolationClose(e);break;case 31:this.stateSpecialStartSequence(e);break;case 32:this.stateInRCDATA(e);break;case 26:this.stateCDATASequence(e);break;case 19:this.stateInAttrValueDoubleQuotes(e);break;case 12:this.stateInAttrName(e);break;case 13:this.stateInDirName(e);break;case 14:this.stateInDirArg(e);break;case 15:this.stateInDynamicDirArg(e);break;case 16:this.stateInDirModifier(e);break;case 28:this.stateInCommentLike(e);break;case 27:this.stateInSpecialComment(e);break;case 11:this.stateBeforeAttrName(e);break;case 6:this.stateInTagName(e);break;case 34:this.stateInSFCRootTagName(e);break;case 9:this.stateInClosingTagName(e);break;case 5:this.stateBeforeTagName(e);break;case 17:this.stateAfterAttrName(e);break;case 20:this.stateInAttrValueSingleQuotes(e);break;case 18:this.stateBeforeAttrValue(e);break;case 8:this.stateBeforeClosingTagName(e);break;case 10:this.stateAfterClosingTagName(e);break;case 29:this.stateBeforeSpecialS(e);break;case 30:this.stateBeforeSpecialT(e);break;case 21:this.stateInAttrValueNoQuotes(e);break;case 7:this.stateInSelfClosingTag(e);break;case 23:this.stateInDeclaration(e);break;case 22:this.stateBeforeDeclaration(e);break;case 25:this.stateBeforeComment(e);break;case 24:this.stateInProcessingInstruction(e);break;case 33:this.stateInEntity()}this.index++}this.cleanup(),this.finish()}cleanup(){this.sectionStart!==this.index&&(1===this.state||32===this.state&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(19===this.state||20===this.state||21===this.state)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}finish(){this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){let e=this.buffer.length;this.sectionStart>=e||(28===this.state?this.currentSequence===eq.CdataEnd?this.cbs.oncdata(this.sectionStart,e):this.cbs.oncomment(this.sectionStart,e):6===this.state||11===this.state||18===this.state||17===this.state||12===this.state||13===this.state||14===this.state||15===this.state||16===this.state||20===this.state||19===this.state||21===this.state||9===this.state||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,t){}}(tY,{onerr:nr,ontext(e,t){t2(t1(e,t),e,t)},ontextentity(e,t,n){t2(e,t,n)},oninterpolation(e,t){if(tW)return t2(t1(e,t),e,t);let n=e+tQ.delimiterOpen.length,i=t-tQ.delimiterClose.length;for(;e$(tB.charCodeAt(n));)n++;for(;e$(tB.charCodeAt(i-1));)i--;let r=t1(n,i);r.includes("&")&&(r=tU.decodeEntities(r,!1)),ne({type:5,content:ni(r,!1,nt(n,i)),loc:nt(e,t)})},onopentagname(e,t){let n=t1(e,t);t$={type:1,tag:n,ns:tU.getNamespace(n,tY[0],tU.ns),tagType:0,props:[],children:[],loc:nt(e-1,t),codegenNode:void 0}},onopentagend(e){t0(e)},onclosetag(e,t){let n=t1(e,t);if(!tU.isVoidTag(n)){let i=!1;for(let e=0;e<tY.length;e++)if(tY[e].tag.toLowerCase()===n.toLowerCase()){i=!0,e>0&&tY[0].loc.start.offset;for(let n=0;n<=e;n++)t3(tY.shift(),t,n<e);break}i||t4(e,60)}},onselfclosingtag(e){let t=t$.tag;t$.isSelfClosing=!0,t0(e),tY[0]&&tY[0].tag===t&&t3(tY.shift(),e)},onattribname(e,t){tH={type:6,name:t1(e,t),nameLoc:nt(e,t),value:void 0,loc:nt(e)}},ondirname(e,t){let n=t1(e,t),i="."===n||":"===n?"bind":"@"===n?"on":"#"===n?"slot":n.slice(2);if(tW||""===i)tH={type:6,name:n,nameLoc:nt(e,t),value:void 0,loc:nt(e)};else if(tH={type:7,name:i,rawName:n,exp:void 0,arg:void 0,modifiers:"."===n?[ey("prop")]:[],loc:nt(e)},"pre"===i){tW=tQ.inVPre=!0,tK=t$;let e=t$.props;for(let t=0;t<e.length;t++)7===e[t].type&&(e[t]=function(e){let t={type:6,name:e.rawName,nameLoc:nt(e.loc.start.offset,e.loc.start.offset+e.rawName.length),value:void 0,loc:e.loc};if(e.exp){let n=e.exp.loc;n.end.offset<e.loc.end.offset&&(n.start.offset--,n.start.column--,n.end.offset++,n.end.column++),t.value={type:2,content:e.exp.content,loc:n}}return t}(e[t]))}},ondirarg(e,t){if(e===t)return;let n=t1(e,t);if(tW)tH.name+=n,nn(tH.nameLoc,t);else{let i="["!==n[0];tH.arg=ni(i?n:n.slice(1,-1),i,nt(e,t),3*!!i)}},ondirmodifier(e,t){let n=t1(e,t);if(tW)tH.name+="."+n,nn(tH.nameLoc,t);else if("slot"===tH.name){let e=tH.arg;e&&(e.content+="."+n,nn(e.loc,t))}else{let i=ey(n,!0,nt(e,t));tH.modifiers.push(i)}},onattribdata(e,t){tG+=t1(e,t),tq<0&&(tq=e),tJ=t},onattribentity(e,t,n){tG+=e,tq<0&&(tq=t),tJ=n},onattribnameend(e){let t=t1(tH.loc.start.offset,e);7===tH.type&&(tH.rawName=t),t$.props.some(e=>(7===e.type?e.rawName:e.name)===t)},onattribend(e,t){if(t$&&tH){if(nn(tH.loc,t),0!==e)if(tG.includes("&")&&(tG=tU.decodeEntities(tG,!0)),6===tH.type)"class"===tH.name&&(tG=t8(tG).trim()),tH.value={type:2,content:tG,loc:1===e?nt(tq,tJ):nt(tq-1,tJ+1)},tQ.inSFCRoot&&"template"===t$.tag&&"lang"===tH.name&&tG&&"html"!==tG&&tQ.enterRCDATA(eG("</template"),0);else{tH.exp=ni(tG,!1,nt(tq,tJ),0,0),"for"===tH.name&&(tH.forParseResult=function(e){let t=e.loc,n=e.content,i=n.match(tX);if(!i)return;let[,r,s]=i,o=(e,n,i=!1)=>{let r=t.start.offset+n,s=r+e.length;return ni(e,!1,nt(r,s),0,+!!i)},a={source:o(s.trim(),n.indexOf(s,r.length)),value:void 0,key:void 0,index:void 0,finalized:!1},l=r.trim().replace(tZ,"").trim(),c=r.indexOf(l),h=l.match(tz);if(h){let e;l=l.replace(tz,"").trim();let t=h[1].trim();if(t&&(e=n.indexOf(t,c+l.length),a.key=o(t,e,!0)),h[2]){let i=h[2].trim();i&&(a.index=o(i,n.indexOf(i,a.key?e+t.length:c+l.length),!0))}}return l&&(a.value=o(l,c,!0)),a}(tH.exp));let e=-1;"bind"===tH.name&&(e=tH.modifiers.findIndex(e=>"sync"===e.content))>-1&&eY("COMPILER_V_BIND_SYNC",tU,tH.loc,tH.arg.loc.source)&&(tH.name="model",tH.modifiers.splice(e,1))}(7!==tH.type||"pre"!==tH.name)&&t$.props.push(tH)}tG="",tq=tJ=-1},oncomment(e,t){tU.comments&&ne({type:3,content:t1(e,t),loc:nt(e-4,t+3)})},onend(){let e=tB.length;for(let t=0;t<tY.length;t++)t3(tY[t],e-1),tY[t].loc.start.offset},oncdata(e,t){0!==tY[0].ns&&t2(t1(e,t),e,t)},onprocessinginstruction(e){(tY[0]?tY[0].ns:tU.ns)===0&&nr(21,e-1)}}),tz=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,tZ=/^\(|\)$/g;function t1(e,t){return tB.slice(e,t)}function t0(e){tQ.inSFCRoot&&(t$.innerLoc=nt(e+1,e+1)),ne(t$);let{tag:t,ns:n}=t$;0===n&&tU.isPreTag(t)&&tj++,tU.isVoidTag(t)?t3(t$,e):(tY.unshift(t$),(1===n||2===n)&&(tQ.inXML=!0)),t$=null}function t2(e,t,n){{let t=tY[0]&&tY[0].tag;"script"!==t&&"style"!==t&&e.includes("&")&&(e=tU.decodeEntities(e,!1))}let i=tY[0]||tF,r=i.children[i.children.length-1];r&&2===r.type?(r.content+=e,nn(r.loc,n)):i.children.push({type:2,content:e,loc:nt(t,n)})}function t3(e,t,n=!1){n?nn(e.loc,t4(t,60)):nn(e.loc,function(e,t){let n=e;for(;62!==tB.charCodeAt(n)&&n<tB.length-1;)n++;return n}(t,62)+1),tQ.inSFCRoot&&(e.children.length?e.innerLoc.end=o({},e.children[e.children.length-1].loc.end):e.innerLoc.end=o({},e.innerLoc.start),e.innerLoc.source=t1(e.innerLoc.start.offset,e.innerLoc.end.offset));let{tag:i,ns:r,children:s}=e;if(!tW&&("slot"===i?e.tagType=2:t5(e)?e.tagType=3:function({tag:e,props:t}){var n;if(tU.isCustomElement(e))return!1;if("component"===e||(n=e.charCodeAt(0))>64&&n<91||to(e)||tU.isBuiltInComponent&&tU.isBuiltInComponent(e)||tU.isNativeTag&&!tU.isNativeTag(e))return!0;for(let e=0;e<t.length;e++){let n=t[e];if(6===n.type){if("is"===n.name&&n.value){if(n.value.content.startsWith("vue:"))return!0;else if(eY("COMPILER_IS_ON_ELEMENT",tU,n.loc))return!0}}else if("bind"===n.name&&tA(n.arg,"is")&&eY("COMPILER_IS_ON_ELEMENT",tU,n.loc))return!0}return!1}(e)&&(e.tagType=1)),tQ.inRCDATA||(e.children=t7(s)),0===r&&tU.isIgnoreNewlineTag(i)){let e=s[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}0===r&&tU.isPreTag(i)&&tj--,tK===e&&(tW=tQ.inVPre=!1,tK=null),tQ.inXML&&(tY[0]?tY[0].ns:tU.ns)===0&&(tQ.inXML=!1);{let t=e.props;if(!tQ.inSFCRoot&&eK("COMPILER_NATIVE_TEMPLATE",tU)&&"template"===e.tag&&!t5(e)){let t=tY[0]||tF,n=t.children.indexOf(e);t.children.splice(n,1,...e.children)}let n=t.find(e=>6===e.type&&"inline-template"===e.name);n&&eY("COMPILER_INLINE_TEMPLATE",tU,n.loc)&&e.children.length&&(n.value={type:2,content:t1(e.children[0].loc.start.offset,e.children[e.children.length-1].loc.end.offset),loc:n.loc})}}function t4(e,t){let n=e;for(;tB.charCodeAt(n)!==t&&n>=0;)n--;return n}let t6=new Set(["if","else","else-if","for","slot"]);function t5({tag:e,props:t}){if("template"===e){for(let e=0;e<t.length;e++)if(7===t[e].type&&t6.has(t[e].name))return!0}return!1}let t9=/\r\n/g;function t7(e,t){let n="preserve"!==tU.whitespace,i=!1;for(let t=0;t<e.length;t++){let r=e[t];if(2===r.type)if(tj)r.content=r.content.replace(t9,`
`);else if(function(e){for(let t=0;t<e.length;t++)if(!e$(e.charCodeAt(t)))return!1;return!0}(r.content)){let s=e[t-1]&&e[t-1].type,o=e[t+1]&&e[t+1].type;!s||!o||n&&(3===s&&(3===o||1===o)||1===s&&(3===o||1===o&&function(e){for(let t=0;t<e.length;t++){let n=e.charCodeAt(t);if(10===n||13===n)return!0}return!1}(r.content)))?(i=!0,e[t]=null):r.content=" "}else n&&(r.content=t8(r.content))}return i?e.filter(Boolean):e}function t8(e){let t="",n=!1;for(let i=0;i<e.length;i++)e$(e.charCodeAt(i))?n||(t+=" ",n=!0):(t+=e[i],n=!1);return t}function ne(e){(tY[0]||tF).children.push(e)}function nt(e,t){return{start:tQ.getPos(e),end:null==t?t:tQ.getPos(t),source:null==t?t:t1(e,t)}}function nn(e,t){e.end=tQ.getPos(t),e.source=t1(e.start.offset,t)}function ni(e,t=!1,n,i=0,r=0){return ey(e,t,n,i)}function nr(e,t,n){tU.onError(e1(e,nt(t,t)))}function ns(e,t){if(tQ.reset(),t$=null,tH=null,tG="",tq=-1,tJ=-1,tY.length=0,tB=e,tU=o({},tw),t){let e;for(e in t)null!=t[e]&&(tU[e]=t[e])}tQ.mode="html"===tU.parseMode?1:2*("sfc"===tU.parseMode),tQ.inXML=1===tU.ns||2===tU.ns;let n=t&&t.delimiters;n&&(tQ.delimiterOpen=eG(n[0]),tQ.delimiterClose=eG(n[1]));let i=tF=eS([],e);return tQ.parse(tB),i.loc=nt(0,e.length),i.children=t7(i.children),tF=null,i}function no(e,t){let{children:n}=e;return 1===n.length&&1===t.type&&!tx(t)}function na(e,t){let{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;let i=n.get(e);if(void 0!==i)return i;let r=e.codegenNode;if(13!==r.type||r.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag&&"math"!==e.tag)return 0;if(void 0!==r.patchFlag)return n.set(e,0),0;{let i=3,s=nc(e,t);if(0===s)return n.set(e,0),0;s<i&&(i=s);for(let r=0;r<e.children.length;r++){let s=na(e.children[r],t);if(0===s)return n.set(e,0),0;s<i&&(i=s)}if(i>1)for(let r=0;r<e.props.length;r++){let s=e.props[r];if(7===s.type&&"bind"===s.name&&s.exp){let r=na(s.exp,t);if(0===r)return n.set(e,0),0;r<i&&(i=r)}}if(r.isBlock){for(let t=0;t<e.props.length;t++)if(7===e.props[t].type)return n.set(e,0),0;t.removeHelper(L),t.removeHelper(eX(t.inSSR,r.isComponent)),r.isBlock=!1,t.helper(ek(t.inSSR,r.isComponent))}return n.set(e,i),i}case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return na(e.content,t);case 4:return e.constType;case 8:let s=3;for(let n=0;n<e.children.length;n++){let i=e.children[n];if(l(i)||c(i))continue;let r=na(i,t);if(0===r)return 0;r<s&&(s=r)}return s;case 20:return 2}}let nl=new Set([K,Y,Q,z]);function nc(e,t){let n=3,i=nh(e);if(i&&15===i.type){let{properties:e}=i;for(let i=0;i<e.length;i++){let r,{key:s,value:o}=e[i],a=na(s,t);if(0===a)return a;if(a<n&&(n=a),0===(r=4===o.type?na(o,t):14===o.type?function e(t,n){if(14===t.type&&!l(t.callee)&&nl.has(t.callee)){let i=t.arguments[0];if(4===i.type)return na(i,n);if(14===i.type)return e(i,n)}return 0}(o,t):0))return r;r<n&&(n=r)}}return n}function nh(e){let t=e.codegenNode;if(13===t.type)return t.props}function nd(e,{filename:t="",prefixIdentifiers:r=!1,hoistStatic:s=!1,hmr:o=!1,cacheHandlers:a=!1,nodeTransforms:c=[],directiveTransforms:h={},transformHoist:d=null,isBuiltInComponent:p=i,isCustomElement:u=i,expressionPlugins:f=[],scopeId:m=null,slotted:S=!0,ssr:g=!1,inSSR:T=!1,ssrCssVars:N="",bindingMetadata:I=n,inline:y=!1,isTS:O=!1,onError:A=ez,onWarn:C=eZ,compatConfig:b}){let v=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),R={filename:t,selfName:v&&_(E(v[1])),prefixIdentifiers:r,hoistStatic:s,hmr:o,cacheHandlers:a,nodeTransforms:c,directiveTransforms:h,transformHoist:d,isBuiltInComponent:p,isCustomElement:u,expressionPlugins:f,scopeId:m,slotted:S,ssr:g,inSSR:T,ssrCssVars:N,bindingMetadata:I,inline:y,isTS:O,onError:A,onWarn:C,compatConfig:b,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],cached:[],constantCache:new WeakMap,temps:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,grandParent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){let t=R.helpers.get(e)||0;return R.helpers.set(e,t+1),e},removeHelper(e){let t=R.helpers.get(e);if(t){let n=t-1;n?R.helpers.set(e,n):R.helpers.delete(e)}},helperString:e=>`_${ed[R.helper(e)]}`,replaceNode(e){R.parent.children[R.childIndex]=R.currentNode=e},removeNode(e){let t=R.parent.children,n=e?t.indexOf(e):R.currentNode?R.childIndex:-1;e&&e!==R.currentNode?R.childIndex>n&&(R.childIndex--,R.onNodeRemoved()):(R.currentNode=null,R.onNodeRemoved()),R.parent.children.splice(n,1)},onNodeRemoved:i,addIdentifiers(e){},removeIdentifiers(e){},hoist(e){l(e)&&(e=ey(e)),R.hoists.push(e);let t=ey(`_hoisted_${R.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache(e,t=!1,n=!1){let i=eR(R.cached.length,e,t,n);return R.cached.push(i),i}};return R.filters=new Set,R}function np(e,t){let n=nd(e,t);nu(e,n),t.hoistStatic&&function e(t,n,i,r=!1,s=!1){let{children:o}=t,l=[];for(let n=0;n<o.length;n++){let a=o[n];if(1===a.type&&0===a.tagType){let e=r?0:na(a,i);if(e>0){if(e>=2){a.codegenNode.patchFlag=-1,l.push(a);continue}}else{let e=a.codegenNode;if(13===e.type){let t=e.patchFlag;if((void 0===t||512===t||1===t)&&nc(a,i)>=2){let t=nh(a);t&&(e.props=i.hoist(t))}e.dynamicProps&&(e.dynamicProps=i.hoist(e.dynamicProps))}}}else if(12===a.type&&(r?0:na(a,i))>=2){l.push(a);continue}if(1===a.type){let n=1===a.tagType;n&&i.scopes.vSlot++,e(a,t,i,!1,s),n&&i.scopes.vSlot--}else if(11===a.type)e(a,t,i,1===a.children.length,!0);else if(9===a.type)for(let n=0;n<a.branches.length;n++)e(a.branches[n],t,i,1===a.branches[n].children.length,s)}let c=!1,h=[];if(l.length===o.length&&1===t.type){if(0===t.tagType&&t.codegenNode&&13===t.codegenNode.type&&a(t.codegenNode.children))t.codegenNode.children=d(eT(t.codegenNode.children)),c=!0;else if(1===t.tagType&&t.codegenNode&&13===t.codegenNode.type&&t.codegenNode.children&&!a(t.codegenNode.children)&&15===t.codegenNode.children.type){let e=p(t.codegenNode,"default");e&&(h.push(i.cached.length),e.returns=d(eT(e.returns)),c=!0)}else if(3===t.tagType&&n&&1===n.type&&1===n.tagType&&n.codegenNode&&13===n.codegenNode.type&&n.codegenNode.children&&!a(n.codegenNode.children)&&15===n.codegenNode.children.type){let e=ty(t,"slot",!0),r=e&&e.arg&&p(n.codegenNode,e.arg);r&&(h.push(i.cached.length),r.returns=d(eT(r.returns)),c=!0)}}if(!c)for(let e of l)h.push(i.cached.length),e.codegenNode=i.cache(e.codegenNode);function d(e){let t=i.cache(e);return s&&i.hmr&&(t.needArraySpread=!0),t}function p(e,t){if(e.children&&!a(e.children)&&15===e.children.type){let n=e.children.properties.find(e=>e.key===t||e.key.content===t);return n&&n.value}}h.length&&1===t.type&&1===t.tagType&&t.codegenNode&&13===t.codegenNode.type&&t.codegenNode.children&&!a(t.codegenNode.children)&&15===t.codegenNode.children.type&&t.codegenNode.children.properties.push(eI("__",ey(JSON.stringify(h),!1))),l.length&&i.transformHoist&&i.transformHoist(o,i,t)}(e,void 0,n,no(e,e.children[0])),t.ssr||function(e,t){let{helper:n}=t,{children:i}=e;if(1===i.length){let n=i[0];if(no(e,n)&&n.codegenNode){let i=n.codegenNode;13===i.type&&ew(i,t),e.codegenNode=i}else e.codegenNode=n}else i.length>1&&(e.codegenNode=eg(t,n(C),void 0,e.children,64,void 0,void 0,!0,void 0,!1))}(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.transformed=!0,e.filters=[...n.filters]}function nu(e,t){t.currentNode=e;let{nodeTransforms:n}=t,i=[];for(let r=0;r<n.length;r++){let s=n[r](e,t);if(s&&(a(s)?i.push(...s):i.push(s)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(k);break;case 5:t.ssr||t.helper(j);break;case 9:for(let n=0;n<e.branches.length;n++)nu(e.branches[n],t);break;case 10:case 11:case 1:case 0:var r=e;let s=0,o=()=>{s--};for(;s<r.children.length;s++){let e=r.children[s];l(e)||(t.grandParent=t.parent,t.parent=r,t.childIndex=s,t.onNodeRemoved=o,nu(e,t))}}t.currentNode=e;let c=i.length;for(;c--;)i[c]()}function nf(e,t){let n=l(e)?t=>t===e:t=>e.test(t);return(e,i)=>{if(1===e.type){let{props:r}=e;if(3===e.tagType&&r.some(tv))return;let s=[];for(let o=0;o<r.length;o++){let a=r[o];if(7===a.type&&n(a.name)){r.splice(o,1),o--;let n=t(e,a,i);n&&s.push(n)}}return s}}}let nE="/*@__PURE__*/",n_=e=>`${ed[e]}: _${ed[e]}`;function nm(e,t={}){let n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:i=!1,filename:r="template.vue.html",scopeId:s=null,optimizeImports:o=!1,runtimeGlobalName:a="Vue",runtimeModuleName:l="vue",ssrRuntimeModuleName:c="vue/server-renderer",ssr:h=!1,isTS:d=!1,inSSR:p=!1}){let u={mode:t,prefixIdentifiers:n,sourceMap:i,filename:r,scopeId:s,optimizeImports:o,runtimeGlobalName:a,runtimeModuleName:l,ssrRuntimeModuleName:c,ssr:h,isTS:d,inSSR:p,source:e.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${ed[e]}`,push(e,t=-2,n){u.code+=e},indent(){f(++u.indentLevel)},deindent(e=!1){e?--u.indentLevel:f(--u.indentLevel)},newline(){f(u.indentLevel)}};function f(e){u.push(`
`+"  ".repeat(e),0)}return u}(e,t);t.onContextCreated&&t.onContextCreated(n);let{mode:i,push:r,prefixIdentifiers:s,indent:o,deindent:a,newline:l,scopeId:c,ssr:h}=n,d=Array.from(e.helpers),p=d.length>0,u=!s&&"module"!==i;var f=e,E=n;let{ssr:_,prefixIdentifiers:m,push:S,newline:g,runtimeModuleName:T,runtimeGlobalName:N,ssrRuntimeModuleName:I}=E,y=Array.from(f.helpers);if(y.length>0&&(S(`const _Vue = ${N}
`,-1),f.hoists.length)){let e=[D,V,k,X,w].filter(e=>y.includes(e)).map(n_).join(", ");S(`const { ${e} } = _Vue
`,-1)}(function(e,t){if(!e.length)return;t.pure=!0;let{push:n,newline:i}=t;i();for(let r=0;r<e.length;r++){let s=e[r];s&&(n(`const _hoisted_${r+1} = `),nN(s,t),i())}t.pure=!1})(f.hoists,E),g(),S("return ");let O=(h?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ");if(r(`function ${h?"ssrRender":"render"}(${O}) {`),o(),u&&(r("with (_ctx) {"),o(),p&&(r(`const { ${d.map(n_).join(", ")} } = _Vue
`,-1),l())),e.components.length&&(nS(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(nS(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),nS(e.filters,"filter",n),l()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r(`
`,0),l()),h||r("return "),e.codegenNode?nN(e.codegenNode,n):r("null"),u&&(a(),r("}")),a(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function nS(e,t,{helper:n,push:i,newline:r,isTS:s}){let o=n("filter"===t?$:"component"===t?U:B);for(let n=0;n<e.length;n++){let a=e[n],l=a.endsWith("__self");l&&(a=a.slice(0,-6)),i(`const ${tD(a,t)} = ${o}(${JSON.stringify(a)}${l?", true":""})${s?"!":""}`),n<e.length-1&&r()}}function ng(e,t){let n=e.length>3;t.push("["),n&&t.indent(),nT(e,t,n),n&&t.deindent(),t.push("]")}function nT(e,t,n=!1,i=!0){let{push:r,newline:s}=t;for(let o=0;o<e.length;o++){let c=e[o];l(c)?r(c,-3):a(c)?ng(c,t):nN(c,t),o<e.length-1&&(n?(i&&r(","),s()):i&&r(", "))}}function nN(e,t){if(l(e))return void t.push(e,-3);if(c(e))return void t.push(t.helper(e));switch(e.type){case 1:case 9:case 11:case 12:nN(e.codegenNode,t);break;case 2:n=e,t.push(JSON.stringify(n.content),-3,n);break;case 4:nI(e,t);break;case 5:var n,i,r,s=e,o=t;let{push:h,helper:d,pure:p}=o;p&&h(nE),h(`${d(j)}(`),nN(s.content,o),h(")");break;case 8:ny(e,t);break;case 3:var u=e,f=t;let{push:E,helper:_,pure:m}=f;m&&E(nE),E(`${_(k)}(${JSON.stringify(u.content)})`,-3,u);break;case 13:let S;var g=e,T=t;let{push:N,helper:I,pure:y}=T,{tag:O,props:A,children:C,patchFlag:b,dynamicProps:v,directives:R,isBlock:x,disableTracking:M,isComponent:P}=g;b&&(S=String(b)),R&&N(I(H)+"("),x&&N(`(${I(L)}(${M?"true":""}), `),y&&N(nE),N(I(x?eX(T.inSSR,P):ek(T.inSSR,P))+"(",-2,g),nT(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map(e=>e||"null")}([O,A,C,S,v]),T),N(")"),x&&N(")"),R&&(N(", "),nN(R,T),N(")"));break;case 14:var D=e,V=t;let{push:X,helper:w,pure:U}=V,F=l(D.callee)?D.callee:w(D.callee);U&&X(nE),X(F+"(",-2,D),nT(D.arguments,V),X(")");break;case 15:!function(e,t){let{push:n,indent:i,deindent:r,newline:s}=t,{properties:o}=e;if(!o.length)return n("{}",-2,e);let a=o.length>1;n(a?"{":"{ "),a&&i();for(let e=0;e<o.length;e++){let{key:i,value:r}=o[e],{push:a}=t;8===i.type?(a("["),ny(i,t),a("]")):i.isStatic?a(tl(i.content)?i.content:JSON.stringify(i.content),-2,i):a(`[${i.content}]`,-3,i),n(": "),nN(r,t),e<o.length-1&&(n(","),s())}a&&r(),n(a?"}":" }")}(e,t);break;case 17:i=e,r=t,ng(i.elements,r);break;case 18:var B=e,$=t;let{push:G,indent:q,deindent:J}=$,{params:W,returns:K,body:Y,newline:Q,isSlot:z}=B;z&&G(`_${ed[eo]}(`),G("(",-2,B),a(W)?nT(W,$):W&&nN(W,$),G(") => "),(Q||Y)&&(G("{"),q()),K?(Q&&G("return "),a(K)?ng(K,$):nN(K,$)):Y&&nN(Y,$),(Q||Y)&&(J(),G("}")),z&&(B.isNonScopedSlot&&G(", undefined, true"),G(")"));break;case 19:var Z=e,ee=t;let{test:et,consequent:en,alternate:er,newline:es}=Z,{push:ea,indent:el,deindent:ec,newline:eh}=ee;if(4===et.type){let e=!tl(et.content);e&&ea("("),nI(et,ee),e&&ea(")")}else ea("("),nN(et,ee),ea(")");es&&el(),ee.indentLevel++,es||ea(" "),ea("? "),nN(en,ee),ee.indentLevel--,es&&eh(),es||ea(" "),ea(": ");let ep=19===er.type;!ep&&ee.indentLevel++,nN(er,ee),!ep&&ee.indentLevel--,es&&ec(!0);break;case 20:var eu=e,ef=t;let{push:eE,helper:e_,indent:em,deindent:eS,newline:eg}=ef,{needPauseTracking:eT,needArraySpread:eN}=eu;eN&&eE("[...("),eE(`_cache[${eu.index}] || (`),eT&&(em(),eE(`${e_(ei)}(-1`),eu.inVOnce&&eE(", true"),eE("),"),eg(),eE("(")),eE(`_cache[${eu.index}] = `),nN(eu.value,ef),eT&&(eE(`).cacheIndex = ${eu.index},`),eg(),eE(`${e_(ei)}(1),`),eg(),eE(`_cache[${eu.index}]`),eS()),eE(")"),eN&&eE(")]");break;case 21:nT(e.body,t,!0,!1)}}function nI(e,t){let{content:n,isStatic:i}=e;t.push(i?JSON.stringify(n):n,-3,e)}function ny(e,t){for(let n=0;n<e.children.length;n++){let i=e.children[n];l(i)?t.push(i,-3):nN(i,t)}}let nO=(e,t)=>{if(5===e.type)e.content=nA(e.content,t);else if(1===e.type){let n=ty(e,"memo");for(let i=0;i<e.props.length;i++){let r=e.props[i];if(7===r.type&&"for"!==r.name){let e=r.exp,i=r.arg;!e||4!==e.type||"on"===r.name&&i||n&&i&&4===i.type&&"key"===i.content||(r.exp=nA(e,t,"slot"===r.name)),i&&4===i.type&&!i.isStatic&&(r.arg=nA(i,t))}}}};function nA(e,t,n=!1,i=!1,r=Object.create(t.identifiers)){return e}function nC(e){return l(e)?e:4===e.type?e.content:e.children.map(nC).join("")}let nb=nf(/^(if|else|else-if)$/,(e,t,n)=>nv(e,t,n,(e,t,i)=>{let r=n.parent.children,s=r.indexOf(e),o=0;for(;s-- >=0;){let e=r[s];e&&9===e.type&&(o+=e.branches.length)}return()=>{i?e.codegenNode=nx(t,o,n):function(e){for(;;)if(19===e.type)if(19!==e.alternate.type)return e;else e=e.alternate;else 20===e.type&&(e=e.value)}(e.codegenNode).alternate=nx(t,o+e.branches.length-1,n)}}));function nv(e,t,n,i){if("else"!==t.name&&(!t.exp||!t.exp.content.trim())){let i=t.exp?t.exp.loc:e.loc;n.onError(e1(28,t.loc)),t.exp=ey("true",!1,i)}if("if"===t.name){var r;let s=nR(e,t),o={type:9,loc:nt((r=e.loc).start.offset,r.end.offset),branches:[s]};if(n.replaceNode(o),i)return i(o,s,!0)}else{let r=n.parent.children,s=r.indexOf(e);for(;s-- >=-1;){let o=r[s];if(o&&3===o.type||o&&2===o.type&&!o.content.trim().length){n.removeNode(o);continue}if(o&&9===o.type){"else-if"===t.name&&void 0===o.branches[o.branches.length-1].condition&&n.onError(e1(30,e.loc)),n.removeNode();let r=nR(e,t);o.branches.push(r);let s=i&&i(o,r,!1);nu(r,n),s&&s(),n.currentNode=null}else n.onError(e1(30,e.loc));break}}}function nR(e,t){let n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!ty(e,"for")?e.children:[e],userKey:tO(e,"key"),isTemplateIf:n}}function nx(e,t,n){return e.condition?ev(e.condition,nL(e,t,n),eC(n.helper(k),['""',"true"])):nL(e,t,n)}function nL(e,t,n){let{helper:i}=n,r=eI("key",ey(`${t}`,!1,em,2)),{children:s}=e,o=s[0];if(1!==s.length||1!==o.type)if(1!==s.length||11!==o.type)return eg(n,i(C),eN([r]),s,64,void 0,void 0,!0,!1,!1,e.loc);else{let e=o.codegenNode;return tM(e,r,n),e}{let e=o.codegenNode,t=tk(e);return 13===t.type&&ew(t,n),tM(t,r,n),e}}let nM=(e,t,n)=>{let{modifiers:i,loc:r}=e,s=e.arg,{exp:o}=e;if(o&&4===o.type&&!o.content.trim()&&(o=void 0),!o){if(4!==s.type||!s.isStatic)return n.onError(e1(52,s.loc)),{props:[eI(s,ey("",!0,r))]};nP(e),o=e.exp}return 4!==s.type?(s.children.unshift("("),s.children.push(') || ""')):s.isStatic||(s.content=`${s.content} || ""`),i.some(e=>"camel"===e.content)&&(4===s.type?s.isStatic?s.content=E(s.content):s.content=`${n.helperString(ee)}(${s.content})`:(s.children.unshift(`${n.helperString(ee)}(`),s.children.push(")"))),!n.inSSR&&(i.some(e=>"prop"===e.content)&&nD(s,"."),i.some(e=>"attr"===e.content)&&nD(s,"^")),{props:[eI(s,o)]}},nP=(e,t)=>{let n=e.arg;e.exp=ey(E(n.content),!1,n.loc)},nD=(e,t)=>{4===e.type?e.isStatic?e.content=t+e.content:e.content=`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},nV=nf("for",(e,t,n)=>{let{helper:i,removeHelper:r}=n;return nk(e,t,n,t=>{let s=eC(i(G),[t.source]),o=tR(e),a=ty(e,"memo"),l=tO(e,"key",!1,!0);l&&7===l.type&&!l.exp&&nP(l);let c=l&&(6===l.type?l.value?ey(l.value.content,!0):void 0:l.exp),h=l&&c?eI("key",c):null,d=4===t.source.type&&t.source.constType>0,p=d?64:l?128:256;return t.codegenNode=eg(n,i(C),void 0,s,p,void 0,void 0,!0,!d,!1,e.loc),()=>{let l,{children:p}=t,u=1!==p.length||1!==p[0].type,f=tx(e)?e:o&&1===e.children.length&&tx(e.children[0])?e.children[0]:null;if(f?(l=f.codegenNode,o&&h&&tM(l,h,n)):u?l=eg(n,i(C),h?eN([h]):void 0,e.children,64,void 0,void 0,!0,void 0,!1):(l=p[0].codegenNode,o&&h&&tM(l,h,n),!d!==l.isBlock&&(l.isBlock?(r(L),r(eX(n.inSSR,l.isComponent))):r(ek(n.inSSR,l.isComponent))),l.isBlock=!d,l.isBlock?(i(L),i(eX(n.inSSR,l.isComponent))):i(ek(n.inSSR,l.isComponent))),a){let e=eb(nw(t.parseResult,[ey("_cached")]));e.body=ex([eA(["const _memo = (",a.exp,")"]),eA(["if (_cached",...c?[" && _cached.key === ",c]:[],` && ${n.helperString(eh)}(_cached, _memo)) return _cached`]),eA(["const _item = ",l]),ey("_item.memo = _memo"),ey("return _item")]),s.arguments.push(e,ey("_cache"),ey(String(n.cached.length))),n.cached.push(null)}else s.arguments.push(eb(nw(t.parseResult),l,!0))}})});function nk(e,t,n,i){if(!t.exp)return void n.onError(e1(31,t.loc));let r=t.forParseResult;if(!r)return void n.onError(e1(32,t.loc));nX(r);let{addIdentifiers:s,removeIdentifiers:o,scopes:a}=n,{source:l,value:c,key:h,index:d}=r,p={type:11,loc:t.loc,source:l,valueAlias:c,keyAlias:h,objectIndexAlias:d,parseResult:r,children:tR(e)?e.children:[e]};n.replaceNode(p),a.vFor++;let u=i&&i(p);return()=>{a.vFor--,u&&u()}}function nX(e,t){e.finalized||(e.finalized=!0)}function nw({value:e,key:t,index:n},i=[]){var r=[e,t,n,...i];let s=r.length;for(;s--&&!r[s];);return r.slice(0,s+1).map((e,t)=>e||ey("_".repeat(t+1),!1))}let nU=ey("undefined",!1),nF=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){let n=ty(e,"slot");if(n)return n.exp,t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},nB=(e,t)=>{let n;if(tR(e)&&e.props.some(tv)&&(n=ty(e,"for"))){let e=n.forParseResult;if(e){nX(e);let{value:n,key:i,index:r}=e,{addIdentifiers:s,removeIdentifiers:o}=t;return n&&s(n),i&&s(i),r&&s(r),()=>{n&&o(n),i&&o(i),r&&o(r)}}}},n$=(e,t,n,i)=>eb(e,n,!1,!0,n.length?n[0].loc:i);function nH(e,t,n=n$){t.helper(eo);let{children:i,loc:r}=e,s=[],o=[],a=t.scopes.vSlot>0||t.scopes.vFor>0,l=ty(e,"slot",!0);if(l){let{arg:e,exp:t}=l;e&&!ts(e)&&(a=!0),s.push(eI(e||ey("default",!0),n(t,void 0,i,r)))}let c=!1,h=!1,d=[],p=new Set,u=0;for(let e=0;e<i.length;e++){let r,f,E,_,m=i[e];if(!tR(m)||!(r=ty(m,"slot",!0))){3!==m.type&&d.push(m);continue}if(l){t.onError(e1(37,r.loc));break}c=!0;let{children:S,loc:g}=m,{arg:T=ey("default",!0),exp:N,loc:I}=r;ts(T)?f=T?T.content:"default":a=!0;let y=ty(m,"for"),O=n(N,y,S,g);if(E=ty(m,"if"))a=!0,o.push(ev(E.exp,nG(T,O,u++),nU));else if(_=ty(m,/^else(-if)?$/,!0)){let n,r=e;for(;r--&&3===(n=i[r]).type;);if(n&&tR(n)&&ty(n,/^(else-)?if$/)){let e=o[o.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=_.exp?ev(_.exp,nG(T,O,u++),nU):nG(T,O,u++)}else t.onError(e1(30,_.loc))}else if(y){a=!0;let e=y.forParseResult;e?(nX(e),o.push(eC(t.helper(G),[e.source,eb(nw(e),nG(T,O),!0)]))):t.onError(e1(32,y.loc))}else{if(f){if(p.has(f)){t.onError(e1(38,I));continue}p.add(f),"default"===f&&(h=!0)}s.push(eI(T,O))}}if(!l){let e=(e,i)=>{let s=n(e,void 0,i,r);return t.compatConfig&&(s.isNonScopedSlot=!0),eI("default",s)};c?d.length&&d.some(e=>(function e(t){return 2!==t.type&&12!==t.type||(2===t.type?!!t.content.trim():e(t.content))})(e))&&(h?t.onError(e1(39,d[0].loc)):s.push(e(void 0,d))):s.push(e(void 0,i))}let f=a?2:!function e(t){for(let n=0;n<t.length;n++){let i=t[n];switch(i.type){case 1:if(2===i.tagType||e(i.children))return!0;break;case 9:if(e(i.branches))return!0;break;case 10:case 11:if(e(i.children))return!0}}return!1}(e.children)?1:3,E=eN(s.concat(eI("_",ey(f+"",!1))),r);return o.length&&(E=eC(t.helper(J),[E,eT(o)])),{slots:E,hasDynamicSlots:a}}function nG(e,t,n){let i=[eI("name",e),eI("fn",t)];return null!=n&&i.push(eI("key",ey(String(n),!0))),eN(i)}let nq=new WeakMap,nJ=(e,t)=>function(){let n,i,r,s,o;if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;let{tag:a,props:l}=e,c=1===e.tagType,d=c?nj(e,t):`"${a}"`,p=h(d)&&d.callee===F,u=0,f=p||d===b||d===v||!c&&("svg"===a||"foreignObject"===a||"math"===a);if(l.length>0){let i=nW(e,t,void 0,c,p);n=i.props,u=i.patchFlag,s=i.dynamicPropNames;let r=i.directives;o=r&&r.length?eT(r.map(e=>nY(e,t))):void 0,i.shouldUseBlock&&(f=!0)}if(e.children.length>0)if(d===R&&(f=!0,u|=1024),c&&d!==b&&d!==R){let{slots:n,hasDynamicSlots:r}=nH(e,t);i=n,r&&(u|=1024)}else if(1===e.children.length&&d!==b){let n=e.children[0],r=n.type,s=5===r||8===r;s&&0===na(n,t)&&(u|=1),i=s||2===r?n:e.children}else i=e.children;s&&s.length&&(r=function(e){let t="[";for(let n=0,i=e.length;n<i;n++)t+=JSON.stringify(e[n]),n<i-1&&(t+=", ");return t+"]"}(s)),e.codegenNode=eg(t,d,n,i,0===u?void 0:u,r,o,!!f,!1,c,e.loc)};function nj(e,t,n=!1){let{tag:i}=e,r=nQ(i),s=tO(e,"is",!1,!0);if(s)if(r||eK("COMPILER_IS_ON_ELEMENT",t)){let e;if(6===s.type?e=s.value&&ey(s.value.content,!0):(e=s.exp)||(e=ey("is",!1,s.arg.loc)),e)return eC(t.helper(F),[e])}else 6===s.type&&s.value.content.startsWith("vue:")&&(i=s.value.content.slice(4));let o=to(i)||t.isBuiltInComponent(i);return o?(n||t.helper(o),o):(t.helper(U),t.components.add(i),tD(i,"component"))}function nW(e,t,n=e.props,i,r,o=!1){let a,{tag:l,loc:h,children:u}=e,f=[],E=[],_=[],m=u.length>0,S=!1,g=0,T=!1,N=!1,I=!1,y=!1,O=!1,A=!1,C=[],b=e=>{f.length&&(E.push(eN(nK(f),h)),f=[]),e&&E.push(e)},v=()=>{t.scopes.vFor>0&&f.push(eI(ey("ref_for",!0),ey("true")))},R=({key:e,value:n})=>{if(ts(e)){let o=e.content,a=s(o);a&&(!i||r)&&"onclick"!==o.toLowerCase()&&"onUpdate:modelValue"!==o&&!d(o)&&(y=!0),a&&d(o)&&(A=!0),a&&14===n.type&&(n=n.arguments[0]),20===n.type||(4===n.type||8===n.type)&&na(n,t)>0||("ref"===o?T=!0:"class"===o?N=!0:"style"===o?I=!0:"key"===o||C.includes(o)||C.push(o),i&&("class"===o||"style"===o)&&!C.includes(o)&&C.push(o))}else O=!0};for(let r=0;r<n.length;r++){let s=n[r];if(6===s.type){let{loc:e,name:n,nameLoc:i,value:r}=s;if("ref"===n&&(T=!0,v()),"is"===n&&(nQ(l)||r&&r.content.startsWith("vue:")||eK("COMPILER_IS_ON_ELEMENT",t)))continue;f.push(eI(ey(n,!0,i),ey(r?r.content:"",!0,r?r.loc:e)))}else{let{name:n,arg:r,exp:a,loc:d,modifiers:u}=s,T="bind"===n,N="on"===n;if("slot"===n){i||t.onError(e1(40,d));continue}if("once"===n||"memo"===n||"is"===n||T&&tA(r,"is")&&(nQ(l)||eK("COMPILER_IS_ON_ELEMENT",t))||N&&o)continue;if((T&&tA(r,"key")||N&&m&&tA(r,"vue:before-update"))&&(S=!0),T&&tA(r,"ref")&&v(),!r&&(T||N)){if(O=!0,a)if(T){if(b(),eK("COMPILER_V_BIND_OBJECT_ORDER",t)){E.unshift(a);continue}v(),b(),E.push(a)}else b({type:14,loc:d,callee:t.helper(Z),arguments:i?[a]:[a,"true"]});else t.onError(e1(T?34:35,d));continue}T&&u.some(e=>"prop"===e.content)&&(g|=32);let I=t.directiveTransforms[n];if(I){let{props:n,needRuntime:i}=I(s,e,t);o||n.forEach(R),N&&r&&!ts(r)?b(eN(n,h)):f.push(...n),i&&(_.push(s),c(i)&&nq.set(s,i))}else!p(n)&&(_.push(s),m&&(S=!0))}}if(E.length?(b(),a=E.length>1?eC(t.helper(W),E,h):E[0]):f.length&&(a=eN(nK(f),h)),O?g|=16:(N&&!i&&(g|=2),I&&!i&&(g|=4),C.length&&(g|=8),y&&(g|=32)),!S&&(0===g||32===g)&&(T||A||_.length>0)&&(g|=512),!t.inSSR&&a)switch(a.type){case 15:let x=-1,L=-1,M=!1;for(let e=0;e<a.properties.length;e++){let t=a.properties[e].key;ts(t)?"class"===t.content?x=e:"style"===t.content&&(L=e):t.isHandlerKey||(M=!0)}let P=a.properties[x],D=a.properties[L];M?a=eC(t.helper(Q),[a]):(P&&!ts(P.value)&&(P.value=eC(t.helper(K),[P.value])),D&&(I||4===D.value.type&&"["===D.value.content.trim()[0]||17===D.value.type)&&(D.value=eC(t.helper(Y),[D.value])));break;case 14:break;default:a=eC(t.helper(Q),[eC(t.helper(z),[a])])}return{props:a,directives:_,patchFlag:g,dynamicPropNames:C,shouldUseBlock:S}}function nK(e){let t=new Map,n=[];for(let o=0;o<e.length;o++){var i,r;let a=e[o];if(8===a.key.type||!a.key.isStatic){n.push(a);continue}let l=a.key.content,c=t.get(l);c?("style"===l||"class"===l||s(l))&&(i=c,r=a,17===i.value.type?i.value.elements.push(r.value):i.value=eT([i.value,r.value],i.loc)):(t.set(l,a),n.push(a))}return n}function nY(e,t){let n=[],i=nq.get(e);i?n.push(t.helperString(i)):(t.helper(B),t.directives.add(e.name),n.push(tD(e.name,"directive")));let{loc:r}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));let t=ey("true",!1,r);n.push(eN(e.modifiers.map(e=>eI(e,t)),r))}return eT(n,e.loc)}function nQ(e){return"component"===e||"Component"===e}let nz=(e,t)=>{if(tx(e)){let{children:n,loc:i}=e,{slotName:r,slotProps:s}=nZ(e,t),o=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r,"{}","undefined","true"],a=2;s&&(o[2]=s,a=3),n.length&&(o[3]=eb([],n,!1,!1,i),a=4),t.scopeId&&!t.slotted&&(a=5),o.splice(a),e.codegenNode=eC(t.helper(q),o,i)}};function nZ(e,t){let n,i='"default"',r=[];for(let t=0;t<e.props.length;t++){let n=e.props[t];if(6===n.type)n.value&&("name"===n.name?i=JSON.stringify(n.value.content):(n.name=E(n.name),r.push(n)));else if("bind"===n.name&&tA(n.arg,"name")){if(n.exp)i=n.exp;else if(n.arg&&4===n.arg.type){let e=E(n.arg.content);i=n.exp=ey(e,!1,n.arg.loc)}}else"bind"===n.name&&n.arg&&ts(n.arg)&&(n.arg.content=E(n.arg.content)),r.push(n)}if(r.length>0){let{props:i,directives:s}=nW(e,t,r,!1,!1);n=i,s.length&&t.onError(e1(36,s[0].loc))}return{slotName:i,slotProps:n}}let n1=(e,t,n,i)=>{let r,{loc:s,modifiers:o,arg:a}=e;if(!e.exp&&!o.length,4===a.type)if(a.isStatic){let e=a.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`),r=ey(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?m(E(e)):`on:${e}`,!0,a.loc)}else r=eA([`${n.helperString(en)}(`,a,")"]);else(r=a).children.unshift(`${n.helperString(en)}(`),r.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let c=n.cacheHandlers&&!l&&!n.inVOnce;if(l){let e=tE(l),t=!(e||tg(l)),n=l.content.includes(";");(t||c&&e)&&(l=eA([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let h={props:[eI(r,l||ey("() => {}",!1,s))]};return i&&(h=i(h)),c&&(h.props[0].value=n.cache(h.props[0].value)),h.props.forEach(e=>e.key.isHandlerKey=!0),h},n0=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{let n,i=e.children,r=!1;for(let e=0;e<i.length;e++){let t=i[e];if(tb(t)){r=!0;for(let r=e+1;r<i.length;r++){let s=i[r];if(tb(s))n||(n=i[e]=eA([t],t.loc)),n.children.push(" + ",s),i.splice(r,1),r--;else{n=void 0;break}}}}if(r&&(1!==i.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find(e=>7===e.type&&!t.directiveTransforms[e.name])||"template"===e.tag)))for(let e=0;e<i.length;e++){let n=i[e];if(tb(n)||8===n.type){let r=[];(2!==n.type||" "!==n.content)&&r.push(n),t.ssr||0!==na(n,t)||r.push("1"),i[e]={type:12,content:n,loc:n.loc,codegenNode:eC(t.helper(X),r)}}}}},n2=new WeakSet,n3=(e,t)=>{if(1===e.type&&ty(e,"once",!0)&&!n2.has(e)&&!t.inVOnce&&!t.inSSR)return n2.add(e),t.inVOnce=!0,t.helper(ei),()=>{t.inVOnce=!1;let e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0,!0))}},n4=(e,t,n)=>{let i,{exp:r,arg:s}=e;if(!r)return n.onError(e1(41,e.loc)),n6();let o=r.loc.source.trim(),a=4===r.type?r.content:o,l=n.bindingMetadata[o];if("props"===l||"props-aliased"===l)return r.loc,n6();if(!a.trim()||!tE(r))return n.onError(e1(42,r.loc)),n6();let c=s||ey("modelValue",!0),h=s?ts(s)?`onUpdate:${E(s.content)}`:eA(['"onUpdate:" + ',s]):"onUpdate:modelValue",d=n.isTS?"($event: any)":"$event";i=eA([`${d} => ((`,r,") = $event)"]);let p=[eI(c,e.exp),eI(h,i)];if(e.modifiers.length&&1===t.tagType){let t=e.modifiers.map(e=>e.content).map(e=>(tl(e)?e:JSON.stringify(e))+": true").join(", "),n=s?ts(s)?`${s.content}Modifiers`:eA([s,' + "Modifiers"']):"modelModifiers";p.push(eI(n,ey(`{ ${t} }`,!1,e.loc,2)))}return n6(p)};function n6(e=[]){return{props:e}}let n5=/[\w).+\-_$\]]/,n9=(e,t)=>{eK("COMPILER_FILTERS",t)&&(5===e.type?n7(e.content,t):1===e.type&&e.props.forEach(e=>{7===e.type&&"for"!==e.name&&e.exp&&n7(e.exp,t)}))};function n7(e,t){if(4===e.type)n8(e,t);else for(let n=0;n<e.children.length;n++){let i=e.children[n];"object"==typeof i&&(4===i.type?n8(i,t):8===i.type?n7(e,t):5===i.type&&n7(i.content,t))}}function n8(e,t){let n=e.content,i=!1,r=!1,s=!1,o=!1,a=0,l=0,c=0,h=0,d,p,u,f,E=[];for(u=0;u<n.length;u++)if(p=d,d=n.charCodeAt(u),i)39===d&&92!==p&&(i=!1);else if(r)34===d&&92!==p&&(r=!1);else if(s)96===d&&92!==p&&(s=!1);else if(o)47===d&&92!==p&&(o=!1);else if(124!==d||124===n.charCodeAt(u+1)||124===n.charCodeAt(u-1)||a||l||c){switch(d){case 34:r=!0;break;case 39:i=!0;break;case 96:s=!0;break;case 40:c++;break;case 41:c--;break;case 91:l++;break;case 93:l--;break;case 123:a++;break;case 125:a--}if(47===d){let e,t=u-1;for(;t>=0&&" "===(e=n.charAt(t));t--);e&&n5.test(e)||(o=!0)}}else void 0===f?(h=u+1,f=n.slice(0,u).trim()):_();function _(){E.push(n.slice(h,u).trim()),h=u+1}if(void 0===f?f=n.slice(0,u).trim():0!==h&&_(),E.length){for(u=0;u<E.length;u++)f=function(e,t,n){n.helper($);let i=t.indexOf("(");if(i<0)return n.filters.add(t),`${tD(t,"filter")}(${e})`;{let r=t.slice(0,i),s=t.slice(i+1);return n.filters.add(r),`${tD(r,"filter")}(${e}${")"!==s?","+s:s}`}}(f,E[u],t);e.content=f,e.ast=void 0}}let ie=new WeakSet,it=(e,t)=>{if(1===e.type){let n=ty(e,"memo");if(!(!n||ie.has(e)))return ie.add(e),()=>{let i=e.codegenNode||t.currentNode.codegenNode;i&&13===i.type&&(1!==e.tagType&&ew(i,t),e.codegenNode=eC(t.helper(ec),[n.exp,eb(void 0,i),"_cache",String(t.cached.length)]),t.cached.push(null))}}};function ii(e){return[[n3,nb,it,nV,n9,nz,nJ,nF,n0],{on:n1,bind:nM,model:n4}]}function ir(e,t={}){let n=t.onError||ez,i="module"===t.mode;!0===t.prefixIdentifiers?n(e1(47)):i&&n(e1(48)),t.cacheHandlers&&n(e1(49)),t.scopeId&&!i&&n(e1(50));let r=o({},t,{prefixIdentifiers:!1}),s=l(e)?ns(e,r):e,[a,c]=ii();return np(s,o({},r,{nodeTransforms:[...a,...t.nodeTransforms||[]],directiveTransforms:o({},c,t.directiveTransforms||{})})),nm(s,r)}let is={DATA:"data",PROPS:"props",PROPS_ALIASED:"props-aliased",SETUP_LET:"setup-let",SETUP_CONST:"setup-const",SETUP_REACTIVE_CONST:"setup-reactive-const",SETUP_MAYBE_REF:"setup-maybe-ref",SETUP_REF:"setup-ref",OPTIONS:"options",LITERAL_CONST:"literal-const"},io=()=>({props:[]}),ia=Symbol(""),il=Symbol(""),ic=Symbol(""),ih=Symbol(""),id=Symbol(""),ip=Symbol(""),iu=Symbol(""),iE=Symbol(""),i_=Symbol(""),im=Symbol("");ep({[ia]:"vModelRadio",[il]:"vModelCheckbox",[ic]:"vModelText",[ih]:"vModelSelect",[id]:"vModelDynamic",[ip]:"withModifiers",[iu]:"withKeys",[iE]:"vShow",[i_]:"Transition",[im]:"TransitionGroup"});let iS={parseMode:"html",isVoidTag:A,isNativeTag:e=>I(e)||y(e)||O(e),isPreTag:e=>"pre"===e,isIgnoreNewlineTag:e=>"pre"===e||"textarea"===e,decodeEntities:function(t,n=!1){return(e||(e=document.createElement("div")),n)?(e.innerHTML=`<div foo="${t.replace(/"/g,"&quot;")}">`,e.children[0].getAttribute("foo")):(e.innerHTML=t,e.textContent)},isBuiltInComponent:e=>"Transition"===e||"transition"===e?i_:"TransitionGroup"===e||"transition-group"===e?im:void 0,getNamespace(e,t,n){let i=t?t.ns:n;if(t&&2===i)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some(e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content))&&(i=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(i=0);else t&&1===i&&("foreignObject"===t.tag||"desc"===t.tag||"title"===t.tag)&&(i=0);if(0===i){if("svg"===e)return 1;if("math"===e)return 2}return i}},ig=e=>{1===e.type&&e.props.forEach((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:ey("style",!0,t.loc),exp:iT(t.value.content,t.loc),modifiers:[],loc:t.loc})})},iT=(e,t)=>ey(JSON.stringify(function(e){let t={};return e.replace(N,"").split(g).forEach(e=>{if(e){let n=e.split(T);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}(e)),!1,t,3);function iN(e,t){return e1(e,t)}let iI={X_V_HTML_NO_EXPRESSION:53,53:"X_V_HTML_NO_EXPRESSION",X_V_HTML_WITH_CHILDREN:54,54:"X_V_HTML_WITH_CHILDREN",X_V_TEXT_NO_EXPRESSION:55,55:"X_V_TEXT_NO_EXPRESSION",X_V_TEXT_WITH_CHILDREN:56,56:"X_V_TEXT_WITH_CHILDREN",X_V_MODEL_ON_INVALID_ELEMENT:57,57:"X_V_MODEL_ON_INVALID_ELEMENT",X_V_MODEL_ARG_ON_ELEMENT:58,58:"X_V_MODEL_ARG_ON_ELEMENT",X_V_MODEL_ON_FILE_INPUT_ELEMENT:59,59:"X_V_MODEL_ON_FILE_INPUT_ELEMENT",X_V_MODEL_UNNECESSARY_VALUE:60,60:"X_V_MODEL_UNNECESSARY_VALUE",X_V_SHOW_NO_EXPRESSION:61,61:"X_V_SHOW_NO_EXPRESSION",X_TRANSITION_INVALID_CHILDREN:62,62:"X_TRANSITION_INVALID_CHILDREN",X_IGNORED_SIDE_EFFECT_TAG:63,63:"X_IGNORED_SIDE_EFFECT_TAG",__EXTEND_POINT__:64,64:"__EXTEND_POINT__"},iy={53:"v-html is missing expression.",54:"v-html will override element children.",55:"v-text is missing expression.",56:"v-text will override element children.",57:"v-model can only be used on <input>, <textarea> and <select> elements.",58:"v-model argument is not supported on plain elements.",59:"v-model cannot be used on file inputs since they are read-only. Use a v-on:change listener instead.",60:"Unnecessary value binding used alongside v-model. It will interfere with v-model's behavior.",61:"v-show is missing expression.",62:"<Transition> expects exactly one child element or component.",63:"Tags with side effect (<script> and <style>) are ignored in client component templates."},iO=t("passive,once,capture"),iA=t("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),iC=t("left,right"),ib=t("onkeyup,onkeydown,onkeypress"),iv=(e,t,n,i)=>{let r=[],s=[],o=[];for(let i=0;i<t.length;i++){let a=t[i].content;"native"===a&&eY("COMPILER_V_ON_NATIVE",n)||iO(a)?o.push(a):iC(a)?ts(e)?ib(e.content.toLowerCase())?r.push(a):s.push(a):(r.push(a),s.push(a)):iA(a)?s.push(a):r.push(a)}return{keyModifiers:r,nonKeyModifiers:s,eventOptionModifiers:o}},iR=(e,t)=>ts(e)&&"onclick"===e.content.toLowerCase()?ey(t,!0):4!==e.type?eA(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,ix=(e,t)=>{1===e.type&&0===e.tagType&&("script"===e.tag||"style"===e.tag)&&t.removeNode()},iL=[ig],iM={cloak:io,html:(e,t,n)=>{let{exp:i,loc:r}=e;return i||n.onError(iN(53,r)),t.children.length&&(n.onError(iN(54,r)),t.children.length=0),{props:[eI(ey("innerHTML",!0,r),i||ey("",!0))]}},text:(e,t,n)=>{let{exp:i,loc:r}=e;return i||n.onError(iN(55,r)),t.children.length&&(n.onError(iN(56,r)),t.children.length=0),{props:[eI(ey("textContent",!0),i?na(i,n)>0?i:eC(n.helperString(j),[i],r):ey("",!0))]}},model:(e,t,n)=>{let i=n4(e,t,n);if(!i.props.length||1===t.tagType)return i;e.arg&&n.onError(iN(58,e.arg.loc));let{tag:r}=t,s=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||s){let o=ic,a=!1;if("input"===r||s){let i=tO(t,"type");if(i){if(7===i.type)o=id;else if(i.value)switch(i.value.content){case"radio":o=ia;break;case"checkbox":o=il;break;case"file":a=!0,n.onError(iN(59,e.loc))}}else tC(t)&&(o=id)}else"select"===r&&(o=ih);a||(i.needRuntime=n.helper(o))}else n.onError(iN(57,e.loc));return i.props=i.props.filter(e=>4!==e.key.type||"modelValue"!==e.key.content),i},on:(e,t,n)=>n1(e,t,n,t=>{let{modifiers:i}=e;if(!i.length)return t;let{key:r,value:s}=t.props[0],{keyModifiers:o,nonKeyModifiers:a,eventOptionModifiers:l}=iv(r,i,n,e.loc);if(a.includes("right")&&(r=iR(r,"onContextmenu")),a.includes("middle")&&(r=iR(r,"onMouseup")),a.length&&(s=eC(n.helper(ip),[s,JSON.stringify(a)])),o.length&&(!ts(r)||ib(r.content.toLowerCase()))&&(s=eC(n.helper(iu),[s,JSON.stringify(o)])),l.length){let e=l.map(_).join("");r=ts(r)?ey(`${r.content}${e}`,!0):eA(["(",r,`) + "${e}"`])}return{props:[eI(r,s)]}}),show:(e,t,n)=>{let{exp:i,loc:r}=e;return i||n.onError(iN(61,r)),{props:[],needRuntime:n.helper(iE)}}};function iP(e,t={}){return ir(e,o({},iS,t,{nodeTransforms:[ix,...iL,...t.nodeTransforms||[]],directiveTransforms:o({},iM,t.directiveTransforms||{}),transformHoist:null}))}function iD(e,t={}){return ns(e,o({},iS,t))}export{x as BASE_TRANSITION,is as BindingTypes,ee as CAMELIZE,et as CAPITALIZE,M as CREATE_BLOCK,k as CREATE_COMMENT,P as CREATE_ELEMENT_BLOCK,V as CREATE_ELEMENT_VNODE,J as CREATE_SLOTS,w as CREATE_STATIC,X as CREATE_TEXT,D as CREATE_VNODE,eJ as CompilerDeprecationTypes,e_ as ConstantTypes,iM as DOMDirectiveTransforms,iI as DOMErrorCodes,iy as DOMErrorMessages,iL as DOMNodeTransforms,eE as ElementTypes,e0 as ErrorCodes,C as FRAGMENT,z as GUARD_REACTIVE_PROPS,eh as IS_MEMO_SAME,el as IS_REF,R as KEEP_ALIVE,W as MERGE_PROPS,K as NORMALIZE_CLASS,Q as NORMALIZE_PROPS,Y as NORMALIZE_STYLE,eu as Namespaces,ef as NodeTypes,L as OPEN_BLOCK,es as POP_SCOPE_ID,er as PUSH_SCOPE_ID,G as RENDER_LIST,q as RENDER_SLOT,U as RESOLVE_COMPONENT,B as RESOLVE_DIRECTIVE,F as RESOLVE_DYNAMIC_COMPONENT,$ as RESOLVE_FILTER,ei as SET_BLOCK_TRACKING,v as SUSPENSE,b as TELEPORT,j as TO_DISPLAY_STRING,Z as TO_HANDLERS,en as TO_HANDLER_KEY,i_ as TRANSITION,im as TRANSITION_GROUP,ti as TS_NODE_TYPES,ea as UNREF,il as V_MODEL_CHECKBOX,id as V_MODEL_DYNAMIC,ia as V_MODEL_RADIO,ih as V_MODEL_SELECT,ic as V_MODEL_TEXT,iu as V_ON_WITH_KEYS,ip as V_ON_WITH_MODIFIERS,iE as V_SHOW,eo as WITH_CTX,H as WITH_DIRECTIVES,ec as WITH_MEMO,tT as advancePositionWithClone,tN as advancePositionWithMutation,tI as assert,ir as baseCompile,ns as baseParse,nY as buildDirectiveArgs,nW as buildProps,nH as buildSlots,eY as checkCompatEnabled,iP as compile,ew as convertToBlock,eT as createArrayExpression,eP as createAssignmentExpression,ex as createBlockStatement,eR as createCacheExpression,eC as createCallExpression,e1 as createCompilerError,eA as createCompoundExpression,ev as createConditionalExpression,iN as createDOMCompilerError,nw as createForLoopParams,eb as createFunctionExpression,eM as createIfStatement,eO as createInterpolation,eN as createObjectExpression,eI as createObjectProperty,eV as createReturnStatement,eS as createRoot,eD as createSequenceExpression,ey as createSimpleExpression,nf as createStructuralDirectiveTransform,eL as createTemplateLiteral,nd as createTransformContext,eg as createVNodeCall,e2 as errorMessages,e8 as extractIdentifiers,ty as findDir,tO as findProp,tX as forAliasRE,nm as generate,S as generateCodeFrame,ii as getBaseTransformPreset,na as getConstantType,tk as getMemoedVNodeCall,eX as getVNodeBlockHelper,ek as getVNodeHelper,tC as hasDynamicKeyVBind,tV as hasScopeRef,ed as helperNameMap,tM as injectProp,to as isCoreComponent,tg as isFnExpression,tm as isFnExpressionBrowser,tS as isFnExpressionNode,te as isFunctionType,e6 as isInDestructureAssignment,e5 as isInNewExpression,tE as isMemberExpression,tu as isMemberExpressionBrowser,tf as isMemberExpressionNode,e4 as isReferencedIdentifier,tl as isSimpleIdentifier,tx as isSlotOutlet,tA as isStaticArgOf,ts as isStaticExp,tt as isStaticProperty,tn as isStaticPropertyKey,tR as isTemplateNode,tb as isText,tv as isVSlot,em as locStub,io as noopDirectiveTransform,iD as parse,iS as parserOptions,nA as processExpression,nk as processFor,nv as processIf,nZ as processSlotOutlet,ep as registerRuntimeHelpers,nj as resolveComponentType,nC as stringifyExpression,tD as toValidAssetId,nF as trackSlotScopes,nB as trackVForSlotScopes,np as transform,nM as transformBind,nJ as transformElement,nO as transformExpression,n4 as transformModel,n1 as transformOn,ig as transformStyle,nu as traverseNode,tr as unwrapTSNode,e7 as walkBlockDeclarations,e9 as walkFunctionParams,e3 as walkIdentifiers,eQ as warnDeprecation};
