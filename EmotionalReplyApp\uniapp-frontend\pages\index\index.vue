<template>
  <view class="page">
    <text class="title">🎉 情感回复助手</text>
    <text class="subtitle">智能分析，贴心回复</text>
    
    <view class="user-info">
      <text class="user-name">{{ nickname }}</text>
      <text class="quota">今日剩余: {{ remainingQuota }}/{{ dailyQuota }}</text>
    </view>
    
    <view class="actions">
      <button class="btn" @click="goToMessage">💬 智能回复</button>
      <button class="btn" @click="goToHistory">📚 历史记录</button>
    </view>
    
    <view class="stats-card">
      <text class="card-title">使用统计</text>
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-number">{{ todayUsage }}</text>
          <text class="stat-label">今日使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ totalUsage }}</text>
          <text class="stat-label">总计使用</text>
        </view>
      </view>
    </view>
    
    <button class="test-btn" @click="showTest">测试功能</button>
  </view>
</template>

<script>
import { HistoryManager, SettingsManager } from '../../utils/storage.js'

export default {
  name: 'IndexPage',
  
  data() {
    return {
      userStats: {
        todayUsage: 0,
        totalUsage: 0,
        dailyQuota: 10,
        favoriteCount: 0
      }
    }
  },
  
  computed: {
    nickname() {
      return '用户昵称'
    },
    remainingQuota() {
      return Math.max(0, this.userStats.dailyQuota - this.userStats.todayUsage)
    },
    dailyQuota() {
      return this.userStats.dailyQuota
    },
    todayUsage() {
      return this.userStats.todayUsage
    },
    totalUsage() {
      return this.userStats.totalUsage
    }
  },
  
  onLoad() {
    console.log('首页加载成功')
    this.loadUserStats()
  },
  
  onShow() {
    // 每次显示页面时刷新统计数据
    this.loadUserStats()
  },
  
  methods: {
    // 加载用户统计数据
    loadUserStats() {
      const history = HistoryManager.getHistory()
      const settings = SettingsManager.getSettings()
      
      // 计算今日使用量
      const today = new Date().toDateString()
      const todayHistory = history.filter(item => {
        const itemDate = new Date(item.createTime).toDateString()
        return itemDate === today
      })
      
      this.userStats = {
        todayUsage: todayHistory.length,
        totalUsage: history.length,
        dailyQuota: settings.dailyQuota || 10,
        favoriteCount: history.filter(item => item.isFavorite).length
      }
    },
    
    goToMessage() {
      uni.navigateTo({
        url: '/pages/message/input'
      })
    },
    
    goToHistory() {
      uni.navigateTo({
        url: '/pages/history/history'
      })
    },
    
    showTest() {
      uni.showToast({
        title: '页面正常工作！',
        icon: 'success'
      })
    }
  }
}
</script>

<style scoped>
.page {
  padding: 40rpx;
  background: #f8f9fa;
  min-height: 100vh;
  text-align: center;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.user-info {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.user-name {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.quota {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn {
  flex: 1;
  background: white;
  border: none;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-row {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.test-btn {
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
</style>
