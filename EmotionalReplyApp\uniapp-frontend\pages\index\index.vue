<template>
  <view class="container">
    <!-- 顶部用户信息 -->
    <view class="header">
      <view class="user-info">
        <image 
          :src="avatar" 
          class="avatar"
          @click="handleAvatarClick"
        />
        <view class="user-details">
          <text class="nickname">{{ nickname }}</text>
          <text class="quota-info">今日剩余: {{ remainingQuota }}/{{ dailyQuota }}</text>
        </view>
      </view>
      
      <!-- VIP标识 -->
      <view v-if="isVip" class="vip-badge">
        <text>VIP</text>
      </view>
    </view>
    
    <!-- 快速操作区域 -->
    <view class="quick-actions">
      <view class="action-card" @click="navigateToMessage">
        <view class="action-icon">
          <text class="iconfont icon-message"></text>
        </view>
        <view class="action-content">
          <text class="action-title">智能回复</text>
          <text class="action-desc">输入消息，生成回复建议</text>
        </view>
      </view>
      
      <view class="action-card" @click="navigateToHistory">
        <view class="action-icon">
          <text class="iconfont icon-history"></text>
        </view>
        <view class="action-content">
          <text class="action-title">历史记录</text>
          <text class="action-desc">查看使用历史和收藏</text>
        </view>
      </view>
    </view>
    
    <!-- 统计数据 -->
    <view class="statistics">
      <text class="section-title">使用统计</text>
      
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ todayUsage }}</text>
          <text class="stat-label">今日使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ totalUsage }}</text>
          <text class="stat-label">总计使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ quotaUsageRate }}%</text>
          <text class="stat-label">配额使用率</text>
        </view>
      </view>
      
      <!-- 配额进度条 -->
      <view class="quota-progress">
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            :style="{ width: quotaUsageRate + '%' }"
          ></view>
        </view>
        <text class="progress-text">{{ todayUsage }}/{{ dailyQuota }}</text>
      </view>
    </view>
    
    <!-- 最近使用 -->
    <view class="recent-usage" v-if="recentReplies.length > 0">
      <text class="section-title">最近使用</text>
      
      <view class="recent-list">
        <view 
          v-for="(item, index) in recentReplies" 
          :key="index"
          class="recent-item"
          @click="viewReplyDetail(item)"
        >
          <view class="recent-content">
            <text class="original-message">{{ item.originalMessage }}</text>
            <text class="reply-text">{{ item.reply }}</text>
          </view>
          <view class="recent-meta">
            <text class="reply-style">{{ getStyleName(item.style) }}</text>
            <text class="reply-time">{{ formatTime(item.createTime) }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 悬浮操作按钮 -->
    <view class="floating-action" @click="quickReply">
      <text class="iconfont icon-add"></text>
    </view>
  </view>
</template>

<script>
// import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'IndexPage',
  
  data() {
    return {
      recentReplies: []
    }
  },
  
  computed: {
    // 模拟用户数据
    nickname() {
      return '用户昵称'
    },
    avatar() {
      return '/static/images/default-avatar.png'
    },
    isVip() {
      return false
    },
    remainingQuota() {
      return 5
    },
    todayUsage() {
      return 5
    },
    quotaUsageRate() {
      return 50
    },
    dailyQuota() {
      return 10
    },
    totalUsage() {
      return 128
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshData()
  },
  
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
  
  methods: {
    // 模拟方法
    
    // 初始化页面
    async initPage() {
      try {
        await this.loadRecentReplies()
      } catch (error) {
        console.error('Init page failed:', error)
      }
    },

    // 刷新数据
    async refreshData() {
      try {
        await this.loadRecentReplies()
      } catch (error) {
        console.error('Refresh data failed:', error)
      }
    },

    // 加载最近回复
    async loadRecentReplies() {
      try {
        // 模拟数据
        this.recentReplies = [
          {
            id: 1,
            originalMessage: '今天心情不太好',
            reply: '听起来你遇到了一些困难，我很关心你。',
            style: 'warm_caring',
            createTime: new Date()
          },
          {
            id: 2,
            originalMessage: '工作压力好大啊',
            reply: '我理解你现在的感受。',
            style: 'rational',
            createTime: new Date()
          }
        ]
      } catch (error) {
        console.error('Load recent replies failed:', error)
      }
    },
    
    // 头像点击
    handleAvatarClick() {
      uni.navigateTo({
        url: '/pages/settings/settings'
      })
    },
    
    // 导航到消息页面
    navigateToMessage() {
      uni.navigateTo({
        url: '/pages/message/input'
      })
    },
    
    // 导航到历史页面
    navigateToHistory() {
      uni.navigateTo({
        url: '/pages/history/history'
      })
    },
    
    // 快速回复
    quickReply() {
      uni.navigateTo({
        url: '/pages/message/input'
      })
    },
    
    // 查看回复详情
    viewReplyDetail(item) {
      uni.navigateTo({
        url: `/pages/reply/generation?message=${encodeURIComponent(item.originalMessage)}&replyId=${item.id}`
      })
    },
    
    // 获取风格名称
    getStyleName(style) {
      const styleMap = {
        warm_caring: '温暖关怀',
        humorous: '幽默风趣',
        rational: '理性分析',
        concise: '简洁直接',
        romantic: '浪漫情话'
      }
      return styleMap[style] || style
    },
    
    // 格式化时间
    formatTime(time) {
      const now = new Date()
      const target = new Date(time)
      const diff = now - target
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return target.toLocaleDateString()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  
  .user-info {
    display: flex;
    align-items: center;
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .user-details {
      display: flex;
      flex-direction: column;
      
      .nickname {
        color: white;
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .quota-info {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
      }
    }
  }
  
  .vip-badge {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    
    text {
      color: white;
      font-size: 24rpx;
      font-weight: bold;
    }
  }
}

.quick-actions {
  margin-bottom: 30rpx;
  
  .action-card {
    display: flex;
    align-items: center;
    padding: 30rpx;
    background: white;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    
    .action-icon {
      width: 80rpx;
      height: 80rpx;
      background: #2196F3;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      
      .iconfont {
        color: white;
        font-size: 36rpx;
      }
    }
    
    .action-content {
      flex: 1;
      
      .action-title {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .action-desc {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

.statistics {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .stats-grid {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30rpx;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 48rpx;
        font-weight: bold;
        color: #2196F3;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
  
  .quota-progress {
    .progress-bar {
      height: 8rpx;
      background: #f0f0f0;
      border-radius: 4rpx;
      overflow: hidden;
      margin-bottom: 16rpx;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #2196F3, #21CBF3);
        transition: width 0.3s ease;
      }
    }
    
    .progress-text {
      font-size: 24rpx;
      color: #666;
      text-align: center;
      display: block;
    }
  }
}

.recent-usage {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 100rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .recent-list {
    .recent-item {
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .recent-content {
        margin-bottom: 16rpx;
        
        .original-message {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .reply-text {
          font-size: 26rpx;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      
      .recent-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .reply-style {
          font-size: 22rpx;
          color: #2196F3;
          background: rgba(33, 150, 243, 0.1);
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
        }
        
        .reply-time {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }
}

.floating-action {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(33, 150, 243, 0.3);
  z-index: 999;
  
  .iconfont {
    color: white;
    font-size: 48rpx;
  }
}
</style>
