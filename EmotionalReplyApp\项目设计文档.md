# 情感回复助手 App 项目设计文档

## 项目概述

### 项目名称
情感回复助手 (Emotional Reply Assistant)

### 项目描述
一款基于Java和Android开发的情感智能app，帮助用户生成合适的回复消息。用户可以复制收到的消息（特别是来自女生的消息），粘贴到我们的应用中，系统会智能分析消息内容和情感色彩，生成多条个性化的回复建议供用户选择。

### 核心功能流程
1. 用户复制外部消息
2. 在app中粘贴消息内容
3. 系统分析消息情感和语境
4. 生成3-5条不同风格的回复建议
5. 用户选择合适的回复并可进行微调
6. 一键复制选中的回复

## 技术架构

### 前端 (Android App)
- **开发语言**: Java + XML
- **最低SDK版本**: API 23 (Android 6.0) - 支持悬浮窗权限
- **目标SDK版本**: API 34 (Android 14)
- **架构模式**: MVP (Model-View-Presenter)
- **主要组件**:
  - MainActivity: 主界面和权限管理
  - FloatingBubbleService: 悬浮气泡服务
  - ReplyDialogActivity: 弹出式回复生成对话框
  - MessageAnalysisFragment: 消息分析界面片段
  - HistoryActivity: 历史记录界面
  - SettingsActivity: 设置界面
- **关键权限**:
  - SYSTEM_ALERT_WINDOW: 悬浮窗显示权限
  - FOREGROUND_SERVICE: 前台服务权限

### 后端服务
- **开发语言**: Java (Spring Boot)
- **数据库**: MySQL 8.0+
- **连接池**: HikariCP
- **ORM框架**: MyBatis Plus
- **AI服务**: 集成第三方NLP API或本地轻量级模型
- **主要模块**:
  - 消息分析模块
  - 情感识别模块
  - 回复生成模块
  - 用户数据管理模块

## 核心交互设计

### 悬浮气泡模式
- **气泡状态**:
  - 小巧的圆形气泡，半透明设计
  - 显示app图标或简单的消息图标
  - 可拖拽到屏幕边缘任意位置
  - 自动吸附到屏幕边缘，避免遮挡内容

- **激活方式**:
  - 单击气泡：弹出快速回复面板
  - 长按气泡：显示更多选项菜单
  - 双击气泡：直接进入主应用界面

- **智能显示**:
  - 检测到聊天应用时自动显示气泡
  - 支持微信、QQ、钉钉等主流聊天软件
  - 非聊天场景时自动隐藏或缩小

- **弹出对话框**:
  - 从气泡位置弹出，带有动画效果
  - 紧凑的垂直布局，适合单手操作
  - 快速粘贴、生成、复制的流程
  - 点击外部区域或返回键自动收起

## 数据库设计

### 数据库架构
- **数据库**: MySQL 8.0+
- **连接池**: HikariCP (高性能连接池)
- **ORM框架**: MyBatis Plus (简化CRUD操作)
- **数据库连接**: 支持读写分离，主从复制

### 核心数据表设计

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) UNIQUE NOT NULL COMMENT '用户唯一标识',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '用户昵称',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    role VARCHAR(20) DEFAULT 'free' COMMENT '用户角色 free:免费用户 premium:高级会员 admin:管理员',
    premium_expire DATETIME COMMENT '高级会员到期时间',
    daily_quota INT DEFAULT 10 COMMENT '每日使用配额',
    used_today INT DEFAULT 0 COMMENT '今日已使用次数',
    total_used BIGINT DEFAULT 0 COMMENT '总使用次数',
    last_used_date DATE COMMENT '最后使用日期',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status TINYINT DEFAULT 1 COMMENT '状态 0:禁用 1:正常 2:冻结'
);
```

#### 2. 用户偏好设置表 (user_preferences)
```sql
CREATE TABLE user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) UNIQUE NOT NULL,
    preferred_styles JSON COMMENT '偏好的回复风格',
    reply_length_preference TINYINT DEFAULT 2 COMMENT '回复长度偏好 1:简短 2:中等 3:详细',
    emotion_sensitivity DECIMAL(3,2) DEFAULT 0.5 COMMENT '情感敏感度',
    auto_copy TINYINT DEFAULT 1 COMMENT '是否自动复制',
    bubble_position JSON COMMENT '气泡位置偏好',
    theme_color VARCHAR(10) DEFAULT '#2196F3' COMMENT '主题色',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

#### 3. 激活码表 (activation_codes)
```sql
CREATE TABLE activation_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(32) UNIQUE NOT NULL COMMENT '激活码',
    code_type VARCHAR(20) NOT NULL COMMENT '激活码类型',
    duration_days INT NOT NULL COMMENT '有效天数',
    batch_id VARCHAR(64) COMMENT '批次ID',
    created_by VARCHAR(64) NOT NULL COMMENT '创建者(管理员ID)',
    used_by VARCHAR(64) COMMENT '使用者用户ID',
    used_time DATETIME COMMENT '使用时间',
    expire_time DATETIME NOT NULL COMMENT '激活码过期时间',
    status TINYINT DEFAULT 0 COMMENT '状态 0:未使用 1:已使用 2:已过期',
    remark VARCHAR(255) COMMENT '备注信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_code (code),
    INDEX idx_batch_id (batch_id),
    INDEX idx_created_by (created_by),
    INDEX idx_status_expire (status, expire_time)
);
```

#### 4. 使用统计表 (usage_statistics)
```sql
CREATE TABLE usage_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) NOT NULL,
    date DATE NOT NULL,
    daily_used_count INT DEFAULT 0 COMMENT '当日使用次数',
    daily_quota INT DEFAULT 10 COMMENT '当日配额',
    quota_exceeded_count INT DEFAULT 0 COMMENT '超额尝试次数',
    most_used_style VARCHAR(20) COMMENT '最常用风格',
    avg_response_time INT DEFAULT 0 COMMENT '平均响应时间(毫秒)',
    success_rate DECIMAL(5,2) DEFAULT 100.00 COMMENT '成功率',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_date (user_id, date),
    INDEX idx_date (date)
);
```

### 数据库配置

#### application.yml 配置示例
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: EmotionalReplyHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

## 功能模块设计

### 1. 悬浮服务模块
- **权限管理**: 引导用户开启悬浮窗权限
- **生命周期管理**: 前台服务保持气泡常驻
- **内存优化**: 气泡状态下最小化内存占用
- **电池优化**: 智能休眠机制，减少电量消耗

### 2. 消息输入模块
- **剪贴板监听**: 自动检测复制的文本
- **手动输入**: 支持用户直接输入消息
- **消息预处理**: 清理格式、表情符号处理
- **消息分类**: 识别消息类型（问候、抱怨、分享、询问等）

### 2. 情感分析模块
- **情感识别**: 分析消息的情感倾向（开心、难过、生气、中性等）
- **语气分析**: 识别正式/非正式、亲密/疏远等语气特征
- **关键词提取**: 提取消息中的关键信息点
- **上下文理解**: 基于历史对话理解当前消息背景

### 3. 回复生成模块
- **多风格生成**: 
  - 温暖关怀型
  - 幽默风趣型
  - 理性分析型
  - 简洁直接型
  - 浪漫情话型
- **个性化调整**: 根据用户偏好调整回复风格
- **长度控制**: 生成不同长度的回复选项
- **表情符号**: 智能添加合适的表情符号

### 4. 用户界面模块
- **悬浮气泡设计**: 聊天时显示为小气泡状态，不干扰正常聊天
- **快速唤醒**: 点击气泡弹出完整对话框界面
- **简洁设计**: 清爽的Material Design风格
- **快速操作**: 一键复制、快速切换回复
- **实时预览**: 实时显示生成的回复效果
- **历史记录**: 保存常用回复和成功案例
- **智能隐藏**: 不使用时自动缩小为气泡状态

## 项目结构

```
EmotionalReplyApp/
├── android-app/                 # Android应用
│   ├── app/
│   │   ├── src/main/java/       # Java源码
│   │   │   └── com/emotional/reply/
│   │   │       ├── activity/    # Activity类
│   │   │       │   ├── MainActivity.java
│   │   │       │   ├── ReplyDialogActivity.java
│   │   │       │   └── SettingsActivity.java
│   │   │       ├── service/     # 服务类
│   │   │       │   ├── FloatingBubbleService.java
│   │   │       │   └── ReplyGenerationService.java
│   │   │       ├── view/        # 自定义视图
│   │   │       │   ├── FloatingBubbleView.java
│   │   │       │   └── QuickReplyDialog.java
│   │   │       ├── fragment/    # Fragment类
│   │   │       ├── adapter/     # 适配器类
│   │   │       ├── model/       # 数据模型
│   │   │       ├── presenter/   # MVP的P层
│   │   │       └── utils/       # 工具类
│   │   │           ├── PermissionUtils.java
│   │   │           └── FloatingWindowUtils.java
│   │   ├── src/main/res/        # 资源文件
│   │   │   ├── layout/          # 布局文件
│   │   │   ├── values/          # 值资源
│   │   │   ├── drawable/        # 图片资源
│   │   │   └── menu/            # 菜单资源
│   │   └── build.gradle         # 应用级构建配置
│   ├── gradle/                  # Gradle配置
│   ├── build.gradle             # 项目级构建配置
│   └── settings.gradle          # 项目设置
├── backend-service/             # 后端服务(可选)
│   ├── src/main/java/           # Java源码
│   │   └── com/emotional/service/
│   │       ├── controller/      # 控制器
│   │       ├── service/         # 业务逻辑
│   │       ├── model/           # 数据模型
│   │       ├── repository/      # 数据访问
│   │       └── config/          # 配置类
│   ├── src/main/resources/      # 资源文件
│   └── pom.xml                  # Maven配置
├── docs/                        # 文档
│   ├── API文档.md
│   ├── 用户手册.md
│   └── 开发指南.md
└── README.md                    # 项目说明
```

## 开发计划

### 第一阶段 (基础功能 - 2周)
- [ ] 搭建Android项目基础架构
- [ ] 实现悬浮窗权限申请和管理
- [ ] 开发基础的悬浮气泡服务
- [ ] 实现气泡的显示、隐藏和拖拽功能
- [ ] 完成基础的弹出对话框界面

### 第二阶段 (核心功能 - 3周)
- [ ] 实现消息输入和剪贴板监听
- [ ] 集成情感分析API
- [ ] 开发多风格回复生成功能
- [ ] 完善弹出对话框的交互逻辑
- [ ] 实现一键复制和快速操作

### 第三阶段 (优化完善 - 2周)
- [ ] 智能气泡显示逻辑（检测聊天应用）
- [ ] 添加历史记录和个性化设置
- [ ] 内存和电池优化
- [ ] 用户体验优化和动画效果
- [ ] 全面测试和调试

### 第四阶段 (高级功能 - 1周)
- [ ] 支持多种聊天应用的适配
- [ ] 数据统计和使用分析
- [ ] 应用图标和启动页设计
- [ ] 发布准备和文档完善

## 技术实现细节

### 悬浮气泡技术实现
```java
// 悬浮气泡服务核心代码结构
public class FloatingBubbleService extends Service {
    private WindowManager windowManager;
    private View bubbleView;
    private WindowManager.LayoutParams params;

    // 创建悬浮窗
    private void createFloatingBubble() {
        // 设置悬浮窗参数
        params = new WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        );
    }

    // 处理触摸事件
    private void setupTouchListener() {
        // 实现拖拽、点击、长按逻辑
    }
}
```

### 权限管理实现
```java
// 权限检查和申请
public class PermissionUtils {
    public static boolean canDrawOverlays(Context context) {
        return Settings.canDrawOverlays(context);
    }

    public static void requestOverlayPermission(Activity activity) {
        Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
        intent.setData(Uri.parse("package:" + activity.getPackageName()));
        activity.startActivityForResult(intent, REQUEST_CODE);
    }
}
```

## 技术难点与解决方案

### 1. 悬浮窗权限和兼容性
- **问题**: 不同Android版本的悬浮窗权限机制差异
- **解决方案**: 适配不同API级别，提供友好的权限引导界面

### 2. 内存和性能优化
- **问题**: 常驻服务可能影响系统性能
- **解决方案**: 智能休眠机制，最小化内存占用，使用前台服务

### 3. 情感分析准确性
- **问题**: 中文情感分析的复杂性
- **解决方案**: 结合多个NLP服务，建立本地规则库

### 4. 回复质量控制
- **问题**: 生成的回复可能不合适或重复
- **解决方案**: 建立回复质量评估机制，用户反馈学习

### 5. 聊天应用检测
- **问题**: 如何智能识别用户正在使用聊天应用
- **解决方案**: 监听前台应用变化，维护聊天应用包名列表

## 商业模式

### 免费版本
- 基础回复生成功能
- 每日使用次数限制
- 基础回复风格

### 付费版本
- 无限制使用
- 高级回复风格
- 个性化定制
- 历史数据分析

## 风险评估

### 技术风险
- AI服务稳定性
- 用户隐私保护
- 应用性能问题

### 市场风险
- 竞品分析需要深入
- 用户接受度不确定
- 商业化路径需要验证

## 下一步行动

1. 创建Android项目基础结构
2. 设计核心界面原型
3. 调研和选择合适的NLP服务
4. 开始MVP版本开发
