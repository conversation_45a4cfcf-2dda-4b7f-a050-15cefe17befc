/**
 * 用户管理工具类
 */

// 用户信息存储键
const USER_INFO_KEY = 'userInfo'
const USER_TOKEN_KEY = 'userToken'
const CURRENT_USER_ID_KEY = 'currentUserId'

/**
 * 用户管理器
 */
export class UserManager {
  
  /**
   * 设置当前用户信息
   * @param {object} userInfo - 用户信息
   */
  static setUserInfo(userInfo) {
    try {
      uni.setStorageSync(USER_INFO_KEY, userInfo)
      if (userInfo.id) {
        uni.setStorageSync(CURRENT_USER_ID_KEY, userInfo.id)
      }
      console.log('用户信息已保存:', userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  }
  
  /**
   * 获取当前用户信息
   * @returns {object|null} 用户信息
   */
  static getUserInfo() {
    try {
      const userInfo = uni.getStorageSync(USER_INFO_KEY)
      return userInfo || null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }
  
  /**
   * 获取当前用户ID
   * @returns {number|null} 用户ID
   */
  static getCurrentUserId() {
    try {
      const userId = uni.getStorageSync(CURRENT_USER_ID_KEY)
      return userId || 1 // 默认返回1作为测试用户ID
    } catch (error) {
      console.error('获取用户ID失败:', error)
      return 1
    }
  }
  
  /**
   * 设置用户Token
   * @param {string} token - 用户Token
   */
  static setToken(token) {
    try {
      uni.setStorageSync(USER_TOKEN_KEY, token)
    } catch (error) {
      console.error('保存Token失败:', error)
    }
  }
  
  /**
   * 获取用户Token
   * @returns {string|null} Token
   */
  static getToken() {
    try {
      return uni.getStorageSync(USER_TOKEN_KEY)
    } catch (error) {
      console.error('获取Token失败:', error)
      return null
    }
  }
  
  /**
   * 清除用户信息
   */
  static clearUserInfo() {
    try {
      uni.removeStorageSync(USER_INFO_KEY)
      uni.removeStorageSync(USER_TOKEN_KEY)
      uni.removeStorageSync(CURRENT_USER_ID_KEY)
      console.log('用户信息已清除')
    } catch (error) {
      console.error('清除用户信息失败:', error)
    }
  }
  
  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  static isLoggedIn() {
    const userInfo = this.getUserInfo()
    const token = this.getToken()
    return !!(userInfo && token)
  }
  
  /**
   * 获取默认用户信息（用于测试）
   * @returns {object} 默认用户信息
   */
  static getDefaultUserInfo() {
    return {
      id: 1,
      username: 'testuser',
      nickname: '测试用户',
      email: '<EMAIL>',
      avatar: '/static/images/default-avatar.png',
      isVip: false,
      dailyQuota: 10,
      todayUsed: 0,
      totalUsed: 0
    }
  }
  
  /**
   * 初始化默认用户（仅在开发模式下使用）
   */
  static initDefaultUser() {
    // 只在开发环境或明确需要时初始化测试用户
    // 正常情况下不自动初始化，保持未登录状态
    console.log('initDefaultUser 被调用，但不自动初始化用户')
  }

  /**
   * 强制初始化测试用户（仅用于开发调试）
   */
  static forceInitTestUser() {
    const defaultUser = this.getDefaultUserInfo()
    this.setUserInfo(defaultUser)
    this.setToken('test_token_' + Date.now())
    console.log('已强制初始化测试用户')
  }
  
  /**
   * 更新用户信息的某个字段
   * @param {string} key - 字段名
   * @param {any} value - 字段值
   */
  static updateUserField(key, value) {
    const userInfo = this.getUserInfo()
    if (userInfo) {
      userInfo[key] = value
      this.setUserInfo(userInfo)
    }
  }
  
  /**
   * 获取用户显示名称
   * @returns {string} 显示名称
   */
  static getDisplayName() {
    const userInfo = this.getUserInfo()
    if (userInfo) {
      return userInfo.nickname || userInfo.username || 'yumu'
    }
    return '未登录'
  }
  
  /**
   * 获取用户头像
   * @returns {string} 头像URL
   */
  static getAvatar() {
    const userInfo = this.getUserInfo()
    if (userInfo && userInfo.avatar) {
      return userInfo.avatar
    }
    return '/static/images/default-avatar.png'
  }
}

export default UserManager
