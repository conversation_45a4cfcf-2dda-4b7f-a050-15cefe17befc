{"pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": "情感回复助手", "navigationBarBackgroundColor": "#2196F3", "navigationBarTextStyle": "white", "backgroundColor": "#f8f9fa"}}, {"path": "pages/message/input", "style": {"navigationBarTitleText": "消息输入", "navigationBarBackgroundColor": "#2196F3", "navigationBarTextStyle": "white"}}, {"path": "pages/reply/generation", "style": {"navigationBarTitleText": "回复生成", "navigationBarBackgroundColor": "#2196F3", "navigationBarTextStyle": "white"}}, {"path": "pages/history/history", "style": {"navigationBarTitleText": "历史记录", "navigationBarBackgroundColor": "#2196F3", "navigationBarTextStyle": "white"}}, {"path": "pages/settings/settings", "style": {"navigationBarTitleText": "设置", "navigationBarBackgroundColor": "#2196F3", "navigationBarTextStyle": "white"}}, {"path": "pages/login/login", "style": {"navigationStyle": "custom", "navigationBarTitleText": ""}}], "globalStyle": {"navigationBarTextStyle": "white", "navigationBarTitleText": "情感回复助手", "navigationBarBackgroundColor": "#2196F3", "backgroundColor": "#f8f9fa", "app-plus": {"background": "#efeff4"}}, "tabBar": {"color": "#7A7E83", "selectedColor": "#2196F3", "borderStyle": "black", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home-active.png", "text": "首页"}, {"pagePath": "pages/message/input", "iconPath": "static/tabbar/message.png", "selectedIconPath": "static/tabbar/message-active.png", "text": "消息"}, {"pagePath": "pages/history/history", "iconPath": "static/tabbar/history.png", "selectedIconPath": "static/tabbar/history-active.png", "text": "历史"}, {"pagePath": "pages/settings/settings", "iconPath": "static/tabbar/settings.png", "selectedIconPath": "static/tabbar/settings-active.png", "text": "设置"}]}, "condition": {"current": 0, "list": [{"name": "首页", "path": "pages/index/index", "query": ""}, {"name": "消息输入", "path": "pages/message/input", "query": ""}, {"name": "回复生成", "path": "pages/reply/generation", "query": "message=测试消息"}]}, "easycom": {"autoscan": true, "custom": {"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}}}