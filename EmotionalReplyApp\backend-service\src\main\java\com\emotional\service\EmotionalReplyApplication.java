package com.emotional.service;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 情感回复助手应用启动类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@SpringBootApplication
@MapperScan("com.emotional.service.mapper")
@EnableCaching
@EnableAsync
@EnableScheduling
public class EmotionalReplyApplication {

    public static void main(String[] args) {
        SpringApplication.run(EmotionalReplyApplication.class, args);
        System.out.println("=================================");
        System.out.println("情感回复助手服务启动成功！");
        System.out.println("API文档地址: http://localhost:8080/api/doc.html");
        System.out.println("=================================");
    }
}
