-- 情感回复助手初始化数据脚本
-- 创建时间: 2024-01-15
-- 作者: YUMU

USE emotional_reply_db;

-- 插入默认用户（用于测试）
INSERT INTO users (username, nickname, email, password, daily_quota, status) VALUES
('testuser', '测试用户', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJ<PERSON>', 10, 0),
('admin', '管理员', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', 100, 0),
('vipuser', 'VIP用户', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 50, 0);

-- 更新VIP用户状态
UPDATE users SET is_vip = 1, vip_expire_time = DATE_ADD(NOW(), INTERVAL 1 YEAR) WHERE username = 'vipuser';

-- 插入用户设置
INSERT INTO user_settings (user_id, theme, font_size, auto_save, reply_styles) VALUES
(1, 'light', 'medium', 1, '["warm_caring", "humorous", "rational", "concise", "romantic"]'),
(2, 'dark', 'large', 1, '["professional", "rational", "concise"]'),
(3, 'light', 'medium', 1, '["romantic", "warm_caring", "humorous", "encouraging"]');

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, config_desc, config_type, is_public) VALUES
('default_daily_quota', '10', '默认每日配额', 'number', 1),
('vip_daily_quota', '50', 'VIP每日配额', 'number', 1),
('max_message_length', '1000', '最大消息长度', 'number', 1),
('supported_emotions', '["开心", "难过", "愤怒", "担心", "兴奋", "平静", "关心", "感谢"]', '支持的情感类型', 'json', 1),
('supported_reply_styles', '{"warm_caring": "温暖关怀", "humorous": "幽默风趣", "rational": "理性分析", "concise": "简洁直接", "romantic": "浪漫情话", "encouraging": "鼓励支持", "professional": "专业正式", "casual": "轻松随意"}', '支持的回复风格', 'json', 1),
('enable_user_registration', 'true', '是否开启用户注册', 'boolean', 1),
('enable_guest_access', 'true', '是否允许游客访问', 'boolean', 1),
('api_rate_limit', '60', 'API调用频率限制（每分钟）', 'number', 0),
('maintenance_mode', 'false', '维护模式', 'boolean', 0),
('app_version', '1.0.0', '应用版本', 'string', 1);

-- 插入示例回复历史（用于测试）
INSERT INTO reply_history (user_id, original_message, emotion_result, emotion_confidence, reply_list, selected_reply, selected_style, is_favorite, process_time, client_ip) VALUES
(1, '今天心情不太好', '难过', 85.5, 
'[{"content":"听起来你遇到了一些困难，我很关心你。如果需要帮助或者想聊聊，我随时都在。","style":"warm_caring","styleName":"温暖关怀"},{"content":"虽然现在有点难过，但记住，彩虹总在风雨后！🌈","style":"humorous","styleName":"幽默风趣"}]', 
'听起来你遇到了一些困难，我很关心你。如果需要帮助或者想聊聊，我随时都在。', 'warm_caring', 1, 1500, '127.0.0.1'),

(1, '工作压力好大啊', '担心', 78.2,
'[{"content":"我能理解你的担心，有什么具体的事情让你焦虑吗？我们一起想办法。","style":"warm_caring","styleName":"温暖关怀"},{"content":"担心往往来自于对未知的恐惧，我们可以列出具体的担忧点，逐一分析解决。","style":"rational","styleName":"理性分析"}]',
'我能理解你的担心，有什么具体的事情让你焦虑吗？我们一起想办法。', 'warm_caring', 0, 1800, '127.0.0.1'),

(1, '想你了', '关心', 92.1,
'[{"content":"感受到了你的关心，真的很温暖。谢谢你这么体贴。","style":"warm_caring","styleName":"温暖关怀"},{"content":"你的关心是我最珍贵的礼物，我也同样关心着你。💕","style":"romantic","styleName":"浪漫情话"}]',
'你的关心是我最珍贵的礼物，我也同样关心着你。💕', 'romantic', 1, 1200, '127.0.0.1'),

(2, '今天开会很顺利', '开心', 88.7,
'[{"content":"很高兴看到你心情不错，这种积极的状态很棒，对身心健康都有好处。","style":"rational","styleName":"理性分析"},{"content":"真为你高兴！","style":"concise","styleName":"简洁直接"}]',
'很高兴看到你心情不错，这种积极的状态很棒，对身心健康都有好处。', 'rational', 0, 1100, '*************'),

(3, '晚上一起吃饭吗？', '关心', 75.3,
'[{"content":"你的关心是我最珍贵的礼物，我也同样关心着你。💕","style":"romantic","styleName":"浪漫情话"},{"content":"感受到了你的关心，真的很温暖。谢谢你这么体贴。","style":"warm_caring","styleName":"温暖关怀"}]',
'你的关心是我最珍贵的礼物，我也同样关心着你。💕', 'romantic', 1, 950, '********');

-- 插入情感统计数据
INSERT INTO emotion_statistics (user_id, emotion, count, date) VALUES
-- 用户1的统计
(1, '难过', 2, CURDATE()),
(1, '关心', 1, CURDATE()),
(1, '开心', 1, DATE_SUB(CURDATE(), INTERVAL 1 DAY)),
(1, '担心', 1, DATE_SUB(CURDATE(), INTERVAL 1 DAY)),

-- 用户2的统计
(2, '开心', 1, CURDATE()),
(2, '平静', 2, DATE_SUB(CURDATE(), INTERVAL 1 DAY)),

-- 用户3的统计
(3, '关心', 1, CURDATE()),
(3, '开心', 2, DATE_SUB(CURDATE(), INTERVAL 1 DAY)),

-- 全局统计（user_id为NULL）
(NULL, '开心', 4, CURDATE()),
(NULL, '难过', 2, CURDATE()),
(NULL, '关心', 2, CURDATE()),
(NULL, '担心', 1, CURDATE()),
(NULL, '平静', 2, DATE_SUB(CURDATE(), INTERVAL 1 DAY));

-- 插入API调用日志示例
INSERT INTO api_call_log (user_id, api_path, http_method, request_params, response_code, response_time, client_ip, user_agent) VALUES
(1, '/emotion/analyze', 'POST', '{"message":"今天心情不太好"}', 200, 1500, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, '/emotion/analyze', 'POST', '{"message":"工作压力好大啊"}', 200, 1800, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, '/history/list', 'GET', '{"page":1,"size":20}', 200, 150, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(2, '/emotion/analyze', 'POST', '{"message":"今天开会很顺利"}', 200, 1100, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'),
(3, '/emotion/styles', 'GET', '{}', 200, 50, '********', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15');

-- 更新用户使用统计
UPDATE users SET today_used = 3, total_used = 5 WHERE id = 1;
UPDATE users SET today_used = 1, total_used = 8 WHERE id = 2;
UPDATE users SET today_used = 1, total_used = 12 WHERE id = 3;
