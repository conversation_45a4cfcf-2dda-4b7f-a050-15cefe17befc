# 情感回复助手 uni-app 前端

## 项目简介

这是情感回复助手应用的 uni-app 前端部分，采用 Vue 3 + uni-app 框架开发，支持 Android、iOS、H5 等多平台。

## 技术栈

- **框架**: uni-app (Vue 3)
- **语言**: JavaScript/TypeScript
- **状态管理**: Vuex
- **UI组件**: uni-ui
- **网络请求**: 封装的 request 工具
- **原生插件**: 悬浮窗插件

## 项目结构

```
uniapp-frontend/
├── pages/                   # 页面目录
│   ├── index/              # 主页
│   ├── message/            # 消息输入页
│   ├── reply/              # 回复生成页
│   ├── history/            # 历史记录页
│   ├── settings/           # 设置页
│   └── login/              # 登录页
├── components/             # 组件目录
│   ├── floating-bubble/    # 悬浮气泡组件
│   ├── reply-card/         # 回复卡片组件
│   └── message-input/      # 消息输入组件
├── api/                    # API接口
│   ├── emotion.js          # 情感分析API
│   ├── reply.js            # 回复生成API
│   └── user.js             # 用户相关API
├── store/                  # 状态管理
│   ├── index.js            # Vuex入口
│   └── modules/            # 模块
├── utils/                  # 工具函数
│   ├── request.js          # 网络请求封装
│   ├── storage.js          # 本地存储
│   └── permission.js       # 权限管理
├── static/                 # 静态资源
├── uni_modules/            # uni-app插件
└── native-plugins/         # 原生插件
```

## 开发环境搭建

### 1. 安装 HBuilderX

下载并安装 HBuilderX 开发工具：
- 官网：https://www.dcloud.io/hbuilderx.html
- 选择正式版，包含 uni-app 开发环境

### 2. 安装依赖

```bash
# 进入项目目录
cd uniapp-frontend

# 安装依赖
npm install
```

### 3. 配置开发环境

1. 在 HBuilderX 中打开项目
2. 配置 manifest.json 中的应用信息
3. 配置 pages.json 中的页面路由

### 4. 运行项目

#### H5 开发调试
```bash
npm run dev:h5
```

#### Android App 调试
1. 连接 Android 设备或启动模拟器
2. 在 HBuilderX 中点击"运行" -> "运行到手机或模拟器"

#### 微信小程序调试
```bash
npm run dev:mp-weixin
```

## 核心功能模块

### 1. 用户认证模块
- 用户登录/注册
- Token 管理
- 权限验证

### 2. 消息处理模块
- 消息输入和预处理
- 剪贴板监听
- 消息历史管理

### 3. 情感分析模块
- 调用后端情感分析 API
- 情感结果展示
- 分析历史记录

### 4. 回复生成模块
- 多风格回复生成
- 回复优化和编辑
- 一键复制功能

### 5. 悬浮窗模块
- 原生悬浮窗插件
- 权限管理
- 气泡交互

## API 接口说明

### 基础配置
```javascript
// utils/request.js
const BASE_URL = 'http://localhost:8080/api'
```

### 主要接口

#### 情感分析
```javascript
// 分析消息情感
analyzeEmotion(message, options)

// 获取分析历史
getEmotionHistory(params)
```

#### 回复生成
```javascript
// 生成回复
generateReply(message, options)

// 根据风格生成
generateReplyByStyle(message, style, options)
```

#### 用户管理
```javascript
// 用户登录
login(username, password)

// 获取用户信息
getUserInfo()
```

## 状态管理

### Vuex 模块

#### user 模块
- 用户信息管理
- 登录状态
- 配额统计

#### message 模块
- 消息输入状态
- 历史消息
- 剪贴板内容

#### settings 模块
- 应用设置
- 用户偏好
- 主题配置

#### reply 模块
- 回复生成状态
- 回复历史
- 收藏管理

## 原生插件开发

### 悬浮窗插件

#### 插件结构
```
native-plugins/android/floating-window/
├── src/main/java/
│   └── com/emotional/plugins/
│       ├── FloatingWindowPlugin.java
│       ├── FloatingBubbleService.java
│       └── PermissionManager.java
└── build.gradle
```

#### 主要功能
- 权限检查和申请
- 悬浮气泡显示/隐藏
- 气泡位置管理
- 点击事件处理

#### 使用方法
```javascript
// 检查权限
const plugin = uni.requireNativePlugin('floating-window-plugin')
plugin.hasOverlayPermission((result) => {
  console.log('Has permission:', result.hasPermission)
})

// 显示悬浮气泡
plugin.showFloatingBubble({
  x: 100,
  y: 200,
  size: 56
}, (result) => {
  console.log('Show result:', result)
})
```

## 构建和发布

### 构建 H5 版本
```bash
npm run build:h5
```

### 构建 App 版本
1. 在 HBuilderX 中选择"发行" -> "原生App-云打包"
2. 配置签名证书和应用信息
3. 选择打包类型（测试版/正式版）
4. 提交云端打包

### 构建小程序版本
```bash
npm run build:mp-weixin
```

## 开发规范

### 1. 代码规范
- 使用 ESLint 进行代码检查
- 遵循 Vue 3 组合式 API 规范
- 统一使用 SCSS 编写样式

### 2. 命名规范
- 页面文件：kebab-case
- 组件文件：PascalCase
- 方法名：camelCase
- 常量：UPPER_SNAKE_CASE

### 3. 注释规范
- 所有 API 接口必须添加注释
- 复杂逻辑必须添加说明
- 组件 props 必须添加类型和说明

## 调试技巧

### 1. 控制台调试
```javascript
// 开发环境下的调试日志
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', data)
}
```

### 2. 网络请求调试
- 在 request.js 中已添加请求/响应拦截器
- 可在控制台查看所有网络请求

### 3. 原生插件调试
- 使用 Android Studio 查看原生日志
- 在插件代码中添加 Log.d() 输出

## 常见问题

### 1. 悬浮窗权限问题
- 确保在 manifest.json 中添加了相关权限
- 在应用启动时引导用户开启权限

### 2. 网络请求失败
- 检查后端服务是否启动
- 确认 API 地址配置正确
- 查看网络权限是否开启

### 3. 原生插件无法使用
- 确保插件已正确配置在 manifest.json 中
- 检查插件版本兼容性
- 重新编译原生插件

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础功能模块
- 完成悬浮窗原生插件

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
