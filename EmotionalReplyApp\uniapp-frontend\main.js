import { createSSRApp } from 'vue'
import App from './App.vue'
import store from './store'

// 引入全局样式
import './static/css/global.css'

// 引入工具函数
import * as utils from './utils'

export function createApp() {
  const app = createSSRApp(App)
  
  // 使用 Pinia 状态管理
  app.use(store)
  
  // 全局属性
  app.config.globalProperties.$utils = utils
  
  // 全局错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('Global error:', err, info)
    uni.showToast({
      title: '系统错误，请稍后重试',
      icon: 'none'
    })
  }
  
  return {
    app
  }
}
