<template>
  <view class="container">
    <!-- 用户信息 -->
    <view class="user-section">
      <view class="user-info">
        <image :src="userInfo.avatar" class="avatar" @click="changeAvatar" />
        <view class="user-details">
          <text class="nickname">{{ userInfo.nickname }}</text>
          <text class="user-type">{{ userInfo.isVip ? 'VIP用户' : '普通用户' }}</text>
        </view>
      </view>
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.todayUsage }}</text>
          <text class="stat-label">今日使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.totalUsage }}</text>
          <text class="stat-label">总计使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.remainingQuota }}</text>
          <text class="stat-label">剩余配额</text>
        </view>
      </view>
    </view>
    
    <!-- 设置选项 -->
    <view class="settings-section">
      <text class="section-title">应用设置</text>
      
      <view class="setting-item" @click="toggleFloatingBubble">
        <text class="setting-label">悬浮气泡</text>
        <switch :checked="settings.showFloatingBubble" @change="onFloatingBubbleChange" />
      </view>
      
      <view class="setting-item" @click="toggleAutoAnalysis">
        <text class="setting-label">自动分析</text>
        <switch :checked="settings.autoAnalysis" @change="onAutoAnalysisChange" />
      </view>
      
      <view class="setting-item" @click="selectDefaultStyle">
        <text class="setting-label">默认回复风格</text>
        <text class="setting-value">{{ getStyleName(settings.defaultStyle) }}</text>
      </view>
      
      <view class="setting-item" @click="selectTheme">
        <text class="setting-label">主题模式</text>
        <text class="setting-value">{{ getThemeName(settings.theme) }}</text>
      </view>
    </view>
    
    <!-- 账户管理 -->
    <view class="account-section">
      <text class="section-title">账户管理</text>
      
      <view class="setting-item" @click="upgradeVip" v-if="!userInfo.isVip">
        <text class="setting-label">升级VIP</text>
        <text class="setting-value premium">立即升级</text>
      </view>
      
      <view class="setting-item" @click="viewProfile">
        <text class="setting-label">个人资料</text>
        <text class="setting-value">></text>
      </view>
      
      <view class="setting-item" @click="changePassword">
        <text class="setting-label">修改密码</text>
        <text class="setting-value">></text>
      </view>
    </view>
    
    <!-- 其他设置 -->
    <view class="other-section">
      <text class="section-title">其他</text>
      
      <view class="setting-item" @click="clearCache">
        <text class="setting-label">清除缓存</text>
        <text class="setting-value">{{ cacheSize }}</text>
      </view>
      
      <view class="setting-item" @click="checkUpdate">
        <text class="setting-label">检查更新</text>
        <text class="setting-value">v1.0.0</text>
      </view>
      
      <view class="setting-item" @click="showAbout">
        <text class="setting-label">关于我们</text>
        <text class="setting-value">></text>
      </view>
      
      <view class="setting-item" @click="feedback">
        <text class="setting-label">意见反馈</text>
        <text class="setting-value">></text>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">
        <text>退出登录</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getReplyStyles } from '../../api/emotion.js'
import { getUserStats } from '../../api/history.js'
import { UserManager } from '../../utils/user.js'

export default {
  name: 'SettingsPage',
  
  data() {
    return {
      userInfo: UserManager.getUserInfo() || {
        nickname: '未登录',
        avatar: '/static/images/default-avatar.png',
        isVip: false,
        todayUsage: 0,
        totalUsage: 0,
        remainingQuota: 0
      },
      settings: {
        showFloatingBubble: true,
        autoAnalysis: false,
        defaultStyle: 'warm_caring',
        theme: 'auto'
      },
      cacheSize: '12.5MB',
      replyStyles: {} // 从API获取的回复风格
    }
  },
  
  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    }
  },

  onLoad() {
    // 检查登录状态
    if (!this.isLoggedIn) {
      // 未登录时显示基本设置，但禁用用户相关功能
      this.userInfo = {
        nickname: '未登录',
        avatar: '/static/images/default-avatar.png',
        isVip: false,
        todayUsage: 0,
        totalUsage: 0,
        remainingQuota: 0
      }
    } else {
      this.loadUserInfo()
    }
    this.loadSettings()
    this.loadReplyStyles()
  },
  
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        // 确保用户信息是最新的
        this.userInfo = UserManager.getUserInfo() || UserManager.getDefaultUserInfo()

        // 尝试从API获取用户统计
        const currentUserId = UserManager.getCurrentUserId()
        const stats = await getUserStats(currentUserId)
        this.userInfo = {
          ...this.userInfo,
          todayUsage: stats.todayUsage || 0,
          totalUsage: stats.totalUsage || 0,
          remainingQuota: stats.remainingQuota || 10
        }
      } catch (error) {
        console.log('获取用户信息失败，使用本地数据:', error)
        this.userInfo = UserManager.getUserInfo() || UserManager.getDefaultUserInfo()
      }
    },
    
    // 加载设置
    loadSettings() {
      const savedSettings = uni.getStorageSync('settings')
      if (savedSettings) {
        this.settings = { ...this.settings, ...savedSettings }
      }
    },

    // 加载回复风格
    async loadReplyStyles() {
      try {
        this.replyStyles = await getReplyStyles()
        console.log('回复风格加载成功:', this.replyStyles)
      } catch (error) {
        console.log('获取回复风格失败，使用默认值:', error)
        // 使用默认风格
        this.replyStyles = {
          warm_caring: '温暖关怀',
          humorous: '幽默风趣',
          rational: '理性分析',
          concise: '简洁直接',
          romantic: '浪漫情话'
        }
      }
    },
    
    // 更换头像
    changeAvatar() {
      if (!this.isLoggedIn) {
        this.showLoginRequired()
        return
      }
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.userInfo.avatar = res.tempFilePaths[0]
          // 更新本地用户信息
          UserManager.updateUserField('avatar', res.tempFilePaths[0])
          // TODO: 上传头像到服务器
          uni.showToast({
            title: '头像已更新',
            icon: 'success'
          })
        }
      })
    },
    
    // 悬浮气泡开关
    onFloatingBubbleChange(e) {
      this.settings.showFloatingBubble = e.detail.value
      this.saveSettings()
    },
    
    // 自动分析开关
    onAutoAnalysisChange(e) {
      this.settings.autoAnalysis = e.detail.value
      this.saveSettings()
    },
    
    // 选择默认风格
    selectDefaultStyle() {
      // 使用从API获取的风格列表
      const styles = Object.keys(this.replyStyles).map(key => ({
        value: key,
        label: this.replyStyles[key]
      }))

      uni.showActionSheet({
        itemList: styles.map(s => s.label),
        success: (res) => {
          this.settings.defaultStyle = styles[res.tapIndex].value
          this.saveSettings()
        }
      })
    },
    
    // 选择主题
    selectTheme() {
      const themes = [
        { value: 'auto', label: '跟随系统' },
        { value: 'light', label: '浅色模式' },
        { value: 'dark', label: '深色模式' }
      ]
      
      uni.showActionSheet({
        itemList: themes.map(t => t.label),
        success: (res) => {
          this.settings.theme = themes[res.tapIndex].value
          this.saveSettings()
        }
      })
    },
    
    // 升级VIP
    upgradeVip() {
      uni.navigateTo({
        url: '/pages/vip/upgrade'
      })
    },
    
    // 查看个人资料
    viewProfile() {
      if (!this.isLoggedIn) {
        uni.showModal({
          title: '需要登录',
          content: '请先登录以查看个人资料',
          showCancel: false,
          success: () => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        })
        return
      }
      uni.navigateTo({
        url: '/pages/user/profile'
      })
    },
    
    // 修改密码
    changePassword() {
      uni.navigateTo({
        url: '/pages/user/change-password'
      })
    },
    
    // 清除缓存
    clearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除所有缓存数据吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除缓存逻辑
            this.cacheSize = '0MB'
            uni.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 检查更新
    checkUpdate() {
      uni.showLoading({
        title: '检查中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '已是最新版本',
          icon: 'success'
        })
      }, 2000)
    },
    
    // 关于我们
    showAbout() {
      uni.navigateTo({
        url: '/pages/about/about'
      })
    },
    
    // 意见反馈
    feedback() {
      uni.navigateTo({
        url: '/pages/feedback/feedback'
      })
    },
    
    // 退出登录
    logout() {
      if (!this.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        })
        return
      }

      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除用户数据
            UserManager.clearUserInfo()

            // 刷新当前页面
            uni.reLaunch({
              url: '/pages/index/index'
            })

            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })
          }
        }
      })
    },

    // 显示登录提示
    showLoginRequired() {
      uni.showModal({
        title: '需要登录',
        content: '请先登录以使用此功能',
        showCancel: false,
        success: () => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
    },
    
    // 保存设置
    saveSettings() {
      uni.setStorageSync('settings', this.settings)
    },
    
    // 获取风格名称
    getStyleName(style) {
      return this.replyStyles[style] || style
    },
    
    // 获取主题名称
    getThemeName(theme) {
      const themeMap = {
        auto: '跟随系统',
        light: '浅色模式',
        dark: '深色模式'
      }
      return themeMap[theme] || theme
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.user-section {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 30rpx;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
    }
    
    .user-details {
      flex: 1;
      
      .nickname {
        display: block;
        color: white;
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .user-type {
        color: rgba(255, 255, 255, 0.8);
        font-size: 26rpx;
      }
    }
  }
  
  .user-stats {
    display: flex;
    justify-content: space-around;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        display: block;
        color: white;
        font-size: 48rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
      }
    }
  }
}

.settings-section, .account-section, .other-section {
  background: white;
  margin-bottom: 20rpx;
  
  .section-title {
    display: block;
    padding: 30rpx 30rpx 20rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
  
  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .setting-label {
      font-size: 28rpx;
      color: #333;
    }
    
    .setting-value {
      font-size: 26rpx;
      color: #666;
      
      &.premium {
        color: #ff6b35;
        font-weight: bold;
      }
    }
  }
}

.logout-section {
  padding: 40rpx 30rpx;
  
  .logout-btn {
    width: 100%;
    height: 88rpx;
    background: #ff4757;
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}
</style>
